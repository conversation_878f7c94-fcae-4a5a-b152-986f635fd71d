/**
* Template Name: GroMart - Web App E-Commerce Shop and Store Mobile Template
* Version: 1.0
* Author: HidraTheme 
* Developed By: HidraTheme  
* Author URL: https://themeforest.net/user/hidratheme

-- NOTE: This is main stylesheet of the template -- 
**/
/*================================================
            Table of contents  
================================================== 
1.  GENERAL 
    1.1 BUTTON
    1.2 FORM
    1.3 PAGINATION
2.  PRELOADER
3.  SIDE NAVIGATION
4.  HEADER
5.  CATEGORY
6.  POPULER SEARCH
7.  PRODUCT 
    7.1 FEATURED PRODUCT
    7.2 VEGETABLE
8.  CART
9.  CHECKOUT
10. WISH LIST
11. CONTACT
12. PROMO
13. SUBSCRIBE
14. SETTING
15. GALLERY
16. TESTIMONIAL
17. 404 PAGE
18. LOGIN & REGISTER FORM
19. PRODUCT PAGE
20. FOOTER
====================================================
            End table content 
===================================================*/
/*====== Link Google Fonts API ========*/
@import url("https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700");
/*==================================================
1. GENERAL 
==================================================*/
body, html {
  font-family: "Poppins", sans-serif;
  min-height: 100%;
  color: #303030;
  padding: 0px;
  margin: 0px;
  font-size: 14px;
  line-height: normal;
  font-weight: 300; }

body {
  padding: 0;
  margin: 0; }

p {
  margin: 5px 0;
  line-height: 20px; }

.section-title {
  color: #d71149;
  font-size: 18px;
  width: 100%;
  text-align: center;
  font-weight: 600;
  padding-top: 10px; }

.theme-secondary-color {
  color: #ff8700; }

.label-checkbox {
  line-height: normal !important; }

[type="checkbox"].filled-in:checked + label::after {
  border: 2px solid #d71149;
  background-color: #d71149; }

.search-wrapper {
  margin: 0;
  padding: 10px 10px;
  color: #303030;
  position: relative; }

.search-wrapper input#search {
  display: block;
  font-weight: 300;
  width: 100%;
  height: 35px;
  margin: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 45px 0 15px;
  border: 0;
  border: 1px solid #c30f42;
  border-radius: 90px;
  background: #ffffff; }

.search-wrapper i.material-icons {
  position: absolute;
  top: 0px;
  right: 20px;
  cursor: pointer;
  color: #303030;
  line-height: auto;
  height: auto; }

body input:focus:not([type]):not([readonly]),
body input[type="text"]:focus:not(.browser-default):not([readonly]),
body input[type="password"]:focus:not(.browser-default):not([readonly]),
body input[type="email"]:focus:not(.browser-default):not([readonly]),
body input[type="url"]:focus:not(.browser-default):not([readonly]),
body input[type="time"]:focus:not(.browser-default):not([readonly]),
body input[type="date"]:focus:not(.browser-default):not([readonly]),
body input[type="datetime"]:focus:not(.browser-default):not([readonly]),
body input[type="datetime-local"]:focus:not(.browser-default):not([readonly]),
body input[type="tel"]:focus:not(.browser-default):not([readonly]),
body input[type="number"]:focus:not(.browser-default):not([readonly]),
body input[type="search"]:focus:not(.browser-default):not([readonly]),
body textarea.materialize-textarea:focus:not([readonly]) {
  border-bottom: 1px solid #d71149;
  -webkit-box-shadow: 0 1px 0 0 #d71149;
  box-shadow: 0 1px 0 0 #d71149; }

body input:focus:not([type]):not([readonly]) + label,
body input[type="text"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="password"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="email"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="url"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="time"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="date"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="datetime"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="datetime-local"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="tel"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="number"]:focus:not(.browser-default):not([readonly]) + label,
body input[type="search"]:focus:not(.browser-default):not([readonly]) + label,
body textarea.materialize-textarea:focus:not([readonly]) + label {
  color: #d71149; }

form input.validate:not([type]) + label,
form input.validate[type="text"]:not(.browser-default) + label,
form input.validate[type="password"]:not(.browser-default) + label,
form input.validate[type="email"]:not(.browser-default) + label,
form input.validate[type="url"]:not(.browser-default) + label,
form input.validate[type="time"]:not(.browser-default) + label,
form input.validate[type="date"]:not(.browser-default) + label,
form input.validate[type="datetime"]:not(.browser-default) + label,
form input.validate[type="datetime-local"]:not(.browser-default) + label,
form input.validate[type="tel"]:not(.browser-default) + label,
form input.validate[type="number"]:not(.browser-default) + label,
form input.validate[type="search"]:not(.browser-default) + label,
form textarea.materialize-textarea.validate + label {
  width: auto; }

.theme-gradation {
  background: -moz-linear-gradient(-45deg, #d46016 0%, rgba(212, 96, 22, 0.97) 2%, rgba(238, 89, 16, 0.44) 44%, rgba(238, 89, 16, 0.22) 60%, rgba(238, 89, 16, 0) 100%);
  background: -webkit-linear-gradient(-45deg, #d46016 0%, rgba(212, 96, 22, 0.97) 2%, rgba(238, 89, 16, 0.44) 44%, rgba(238, 89, 16, 0.22) 60%, rgba(238, 89, 16, 0) 100%);
  background: -webkit-linear-gradient(315deg, #d46016 0%, rgba(212, 96, 22, 0.97) 2%, rgba(238, 89, 16, 0.44) 44%, rgba(238, 89, 16, 0.22) 60%, rgba(238, 89, 16, 0) 100%);
  background: -o-linear-gradient(315deg, #d46016 0%, rgba(212, 96, 22, 0.97) 2%, rgba(238, 89, 16, 0.44) 44%, rgba(238, 89, 16, 0.22) 60%, rgba(238, 89, 16, 0) 100%);
  background: linear-gradient(135deg, #d46016 0%, rgba(212, 96, 22, 0.97) 2%, rgba(238, 89, 16, 0.44) 44%, rgba(238, 89, 16, 0.22) 60%, rgba(238, 89, 16, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d46016', endColorstr='#00ee5910',GradientType=1 ); }

body a {
  color: #d71149; }

img {
  max-width: 100%;
  max-height: 100%; }

.clear {
  clear: both; }

.owl-dots {
  width: 100%;
  text-align: center;
  margin-top: 10px; }

.owl-dots .owl-dot {
  display: inline-block;
  margin: 0px 6px 0px 0px;
  height: 10px;
  width: 10px;
  border: 1px solid #d71149;
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px; }

.owl-dots .owl-dot.active {
  width: 20px;
  background: #d71149; }

#page-content {
  padding: 0px 0px; }

.row-no-margin {
  margin-bottom: 0px; }

/*---------- BUTTON ----------*/
.more-btn {
  text-decoration: none; }

.readmore-btn {
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px;
  padding: 3px 10px;
  color: #ffffff;
  background-color: #d71149;
  display: inline-block;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600; }

.readmore-btn:hover {
  background-color: #c30f42; }

body .btn,
body .btn-large {
  background-color: #d71149; }

.theme-btn-rounded {
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px; }

form .btn,
form .btn-large {
  margin-top: 20px; }

body .btn.transparent {
  border: 1px solid #ffffff;
  background-color: transparent; }

body .btn:hover {
  cursor: pointer;
  background-color: #c30f42; }

.main-slider {
  margin-bottom: 10px; }

/*---------- FORM ----------*/
form a.forgot {
  font-size: 12px;
  color: #aaaaaa; }

form .row-forgot {
  margin-top: 20px; }

form .row {
  margin-bottom: 0px; }

/*---------- PAGINATION ----------*/
body .pagination li.active {
  background-color: #d71149; }

ul.pagination {
  margin-top: 30px;
  text-align: center; }

/*==================================================
2. PRELOADER
==================================================*/
.preloading {
  width: 100%;
  text-align: center;
  height: 100%;
  position: fixed;
  background: #c30f42;
  z-index: 999999;
  top: 0px;
  display: table;
  left: 0; }

.preloading .wrap-preload {
  display: table-cell;
  text-align: center;
  vertical-align: middle; }

.preloading .cssload-loader {
  width: 49px;
  height: 49px;
  border-radius: 50%;
  margin: 3em;
  display: inline-block;
  position: relative;
  vertical-align: middle;
  background: rgba(255, 99, 56, 0.95); }

.preloading .cssload-loader,
.preloading .cssload-loader:before,
.preloading .cssload-loader:after {
  animation: 1.15s infinite ease-in-out;
  -o-animation: 1.15s infinite ease-in-out;
  -ms-animation: 1.15s infinite ease-in-out;
  -webkit-animation: 1.15s infinite ease-in-out;
  -moz-animation: 1.15s infinite ease-in-out; }

.preloading .cssload-loader:before,
.preloading .cssload-loader:after {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0; }

.preloading .cssload-loader {
  animation-name: cssload-loader;
  -o-animation-name: cssload-loader;
  -ms-animation-name: cssload-loader;
  -webkit-animation-name: cssload-loader;
  -moz-animation-name: cssload-loader; }

@keyframes cssload-loader {
  from {
    transform: scale(0);
    opacity: 1; }
  to {
    transform: scale(1);
    opacity: 0; } }
@-o-keyframes cssload-loader {
  from {
    -o-transform: scale(0);
    opacity: 1; }
  to {
    -o-transform: scale(1);
    opacity: 0; } }
@-ms-keyframes cssload-loader {
  from {
    -ms-transform: scale(0);
    opacity: 1; }
  to {
    -ms-transform: scale(1);
    opacity: 0; } }
@-webkit-keyframes cssload-loader {
  from {
    -webkit-transform: scale(0);
    opacity: 1; }
  to {
    -webkit-transform: scale(1);
    opacity: 0; } }
@-moz-keyframes cssload-loader {
  from {
    -moz-transform: scale(0);
    opacity: 1; }
  to {
    -moz-transform: scale(1);
    opacity: 0; } }
/*==================================================
3. SIDE NAVIGATION
==================================================*/
body nav {
  height: 56px !important; }

nav .collapsible-header i,
nav .side-nav li > a > i,
nav .side-nav li > a > [class^="mdi-"],
nav .side-nav li > a li > a > [class*="mdi-"],
nav .side-nav li > a > i.material-icons {
  height: auto;
  padding: 0px 0px;
  margin: 0px 0px;
  line-height: normal !important;
  color: #ffffff;
  font-size: 14px;
  width: 24px;
  text-align: left; }

nav .side-nav {
  background-color: #d71149;
  color: #ffffff;
  height: 100%; }

nav .side-nav .sidenav-logo .fa {
  height: auto; }

nav .side-nav .sidenav-logo {
  text-align: center;
  padding: 10px 20px 0px;
  font-size: 20px;
  font-weight: 600; }

nav .side-nav li a {
  color: #ffffff;
  padding: 10px 25px; }

nav .side-nav li .collapsible-header {
  padding: 10px 0px 10px 25px; }

nav .side-nav li a,
nav .side-nav li .collapsible-header {
  font-weight: 300;
  border-top: 1px solid #c30f42;
  line-height: normal;
  height: auto;
  display: block; }

nav .side-nav .collapsible-body ul li a {
  padding: 10px 25px 10px 25px; }

nav .side-nav li .fas,
nav .side-nav li .fab,
nav .side-nav li .far {
  color: #ffffff;
  margin-top: 2px; }

nav .side-nav li .fa.fa-sign-out,
nav .side-nav li .fa.fa-cog {
  font-size: 16px; }

nav .side-nav li .collapsible-header span i {
  float: right;
  color: #fff;
  font-size: 14px;
  line-height: normal;
  height: auto; }

nav ul.side-nav li ul.collapsible .collapsible-body ul li .fas {
  color: #ffffff; }

nav .side-nav li.divider {
  margin-bottom: 20px;
  background-color: rgba(0, 0, 0, 0.2); }

nav .side-nav .collapsible-body,
nav .side-nav.fixed .collapsible-body {
  background-color: rgba(0, 0, 0, 0.05); }

nav .side-nav .profile {
  background-color: #000000;
  text-align: center;
  position: relative;
  padding: 30px 20px; }

nav .side-nav .profile .emailprofile {
  font-size: 12px;
  line-height: normal;
  padding: 5px 0px 0px; }

nav .side-nav .profile .li-profile-info {
  position: relative; }

nav .side-nav .profile .li-profile-info {
  position: relative;
  z-index: 2; }

nav .side-nav .profile .bg-profile-li {
  position: absolute;
  z-index: 0;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background-size: 100% auto; }

nav .side-nav .profile .balance {
  line-height: normal;
  background: #ffffff;
  color: #303030;
  padding: 5px 0px;
  display: block;
  width: 150px;
  margin: 10px auto 0px;
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px; }

nav .side-nav .profile .balance span {
  font-weight: 600; }

nav .side-nav .profile img {
  border-radius: 50%;
  width: 80px;
  height: 80px; }

nav .side-nav .profile h2 {
  font-size: 20px;
  cursor: default;
  margin: 0px 0px;
  font-weight: 600; }

nav .side-nav .profile h6 {
  color: #fff;
  font-size: 15px; }

/*==================================================
4. HEADER
==================================================*/
header {
  height: 56px;
  background-color: #d71149;
  position: fixed;
  width: 100%;
  left: 0px;
  top: 0px;
  z-index: 5;
  border-bottom: 1px solid #c30f42;
  -webkit-box-shadow: 0px 2px 6px 0px rgba(50, 50, 50, 0.4);
  -moz-box-shadow: 0px 2px 6px 0px rgba(50, 50, 50, 0.4);
  box-shadow: 0px 2px 6px 0px rgba(50, 50, 50, 0.4); }

header .header-icon-menu {
  float: right;
  width: 50px;
  height: 56px;
  min-height: 1px;
  text-align: center; }

.header-menu-button {
  float: right;
  width: 40px;
  height: 56px; }

header .header-icon-menu a {
  display: block; }

header .header-icon-menu .fas,
header .header-icon-menu .far {
  font-size: 20px;
  color: #ffffff;
  padding: 18px 13px; }

header .header-icon-menu .fas.fa-shopping-cart {
  font-size: 18px;
  padding: 19px 13px; }

header .header-logo {
  text-align: center;
  line-height: normal;
  float: left; }

header .nav-logo {
  color: #ffffff;
  display: inline-block;
  font-size: 24px;
  font-weight: bold;
  padding: 10px 0px 0px; }

@media screen and (min-width: 0px) and (max-width: 380px) {
  header .nav-logo {
    font-size: 18px;
    padding: 15px 0px 0px; } }
header .nav-logo .fab {
  color: #ff8700;
  height: auto; }

header .nav-logo img {
  max-width: 100%;
  height: 30px;
  margin-top: 13px; }

header .cst-btn-menu {
  padding: 18px 13px; }

header .header-icon-menu.cart-item-wrap {
  position: relative; }

header .header-icon-menu.cart-item-wrap .cart-item {
  color: #ffffff;
  position: absolute;
  top: 10px;
  right: 2px;
  min-height: 20px;
  min-width: 20px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  background: #e93619;
  text-align: center;
  font-size: 11px;
  -webkit-box-shadow: 1px 1px 0px 0px rgba(50, 50, 50, 0.59);
  -moz-box-shadow: 1px 1px 0px 0px rgba(50, 50, 50, 0.59);
  box-shadow: 1px 1px 0px 0px rgba(50, 50, 50, 0.59); }

.header-menu-button a {
  color: #ffffff;
  margin: 0px 0px 0px 0px;
  float: right; }

.header-menu-button a .fas {
  font-size: 20px; }

nav {
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 0px 0px transparent;
  -moz-box-shadow: 0px 0px 0px 0px transparent;
  box-shadow: 0px 0px 0px 0px transparent; }

nav ul a {
  color: #333333; }

nav a.button-collapse {
  display: block !important; }

.section h5 {
  font-weight: 500;
  margin: 0 0 10px 0;
  font-size: 16px; }

/*==================================================
5. CATEGORY
==================================================*/
.section.home-category {
  background-color: #ffffff; }

.section.home-category .row.icon-service {
  margin-bottom: 0px; }

.section.home-category .content {
  border: 1px solid #d1d1d1;
  text-align: center;
  padding-bottom: 100%;
  height: 0px;
  width: 100%;
  position: relative;
  margin-bottom: 20px; }

.section.home-category .content .in-content {
  width: 100%;
  height: 100%;
  display: table;
  position: absolute; }

.section.home-category .content .in-content .in-in-content {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  padding: 15px; }

.section.home-category .content .in-content .in-in-content img {
  width: 60%;
  height: auto; }

.section.home-category i {
  font-size: 30px;
  color: #342580;
  margin-bottom: 5px; }

.section.home-category .content .in-content .in-in-content h5 {
  font-weight: 600;
  font-size: 10px; }

/*==================================================
6. POPULER SEARCH
==================================================*/
.populer-search {
  padding-top: 0px;
  padding-bottom: 0px; }

.populer-search .row .section-title {
  padding-top: 0px;
  margin-top: 0px; }

.populer-search .title-ps {
  font-size: 18px;
  text-align: center;
  text-transform: uppercase;
  padding-bottom: 20px; }

.populer-search .list-tag-word {
  text-align: center; }

.populer-search .tag-word {
  background-color: #d71149;
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px;
  color: #ffffff;
  padding: 5px 20px;
  margin-bottom: 5px;
  display: inline-block;
  cursor: pointer; }

/*==================================================
7. PRODUCT 
==================================================*/
.section.product-item .section-title {
  text-align: center; }

#page-content .section.product-item .section-title {
  text-align: center; }

#page-content .section.product-item {
  padding-bottom: 0px; }

.section.product-item .slick-prev::before,
.section.product-item .slick-next::before {
  color: #d71149; }

.section.product-item .slick-prev,
.section.product-item .slick-next {
  left: 0px !important;
  top: auto;
  bottom: -30px; }

.section.product-item .slick-next {
  left: 23px !important; }

.box-product {
  border: 1px solid #e0e0e0;
  padding: 10px;
  margin-bottom: 20px;
  text-align: center; }

.box-product .bp-top {
  width: 100%;
  overflow: hidden; }

.box-product .bp-top h5 {
  min-height: 0px; }

.box-product .bp-top .bp-top-b {
  min-height: 100px; }

@media screen and (max-width: 320px) {
  .product-item .container .row .col-product {
    width: 100%; } }
.box-product .bp-bottom {
  margin-bottom: 15px; }

.box-product .product-list-img {
  overflow: hidden;
  height: 0px;
  width: 100%;
  padding-bottom: 70%;
  position: relative; }

.box-product .product-list-img .pli-one {
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: table;
  position: absolute;
  text-align: center; }

.box-product .product-list-img .pli-two {
  display: table-cell;
  text-align: center;
  vertical-align: middle; }

.box-product .product-list-img .pli-two img {
  width: 60%;
  height: auto;
  margin: 0 auto; }

.box-product .price {
  font-weight: 600;
  font-size: 16px; }

.box-product .price span {
  font-weight: 400;
  color: #838181;
  font-size: 12px; }

.box-product .stock-item {
  font-style: italic;
  font-size: 10px;
  color: #e60d0d;
  font-weight: 600;
  padding: 0px 0px 20px;
  height: 35px; }

.box-product .item-info {
  font-size: 12px;
  height: 24px;
  overflow: hidden; }

.box-product .button-add-cart {
  font-size: 12px;
  font-weight: 600;
  font-size: 12px;
  font-weight: 600;
  width: 80px;
  margin: 0 auto;
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px;
  text-align: center;
  padding: 1px 0px 0px; }

.slick-product {
  margin-bottom: 0px; }

/*---------- FEATURED PRODUCT ----------*/
.section.product-item.si-featured {
  padding-top: 10px; }

.section.product-item.si-featured .section-title {
  padding-top: 0px; }

.section.product-item.vegetable-list-homepage .slick-prev::before,
.section.product-item.vegetable-list-homepage .slick-next::before {
  color: #ffffff; }

.featured-product .box-product {
  width: 100%;
  padding: 5px; }

.slick-list.draggable {
  margin-left: -5px;
  margin-right: -5px; }

.slick-initialized .slick-slide {
  opacity: 1;
  padding: 0px 5px; }

.slick-slide img {
  width: 100%; }

.slick-prev:before,
.slick-next:before {
  color: black; }

.slick-slide {
  transition: all ease-in-out .3s;
  opacity: .2; }

.slick-active {
  opacity: .5; }

.slick-current {
  opacity: 1; }

.more-product-list {
  text-align: right;
  color: #d71149; }

/*---------- VEGETABLE ----------*/
.vegetable-list-homepage {
  background-color: #d71149;
  padding-top: 25px;
  padding-bottom: 35px; }

.vegetable-list-homepage .featured-product .box-product {
  background-color: #ffffff; }

.vegetable-list-homepage .theme-secondary-color {
  color: #fff; }

.vegetable-list-homepage a.more-btn {
  color: #ffffff; }

/*==================================================
8. CART
==================================================*/
.cart-page {
  padding: 20px 0px 30px; }

.cart-box {
  margin-bottom: 20px; }

.cart-box .cart-item {
  width: 100%;
  position: relative;
  min-height: 70px;
  border-bottom: 1px solid #f2f2f2; }

.cart-box .cart-item .ci-img {
  width: 70px;
  position: absolute;
  left: 0px;
  top: 0px; }

.cart-box .cart-item .ci-img .ci-img-product {
  width: 100%;
  height: 70px;
  position: relative;
  background-size: 70% auto;
  background-position: center center;
  background-repeat: no-repeat; }

.cart-box .cart-item .ci-img .ci-qty {
  height: 30px; }

.cart-box .cart-item .ci-name {
  padding-left: 70px;
  height: 100%;
  padding-right: 105px; }

.cart-box .cart-item .ci-name .cin-top {
  padding: 15px 10px; }

.cart-box .cart-item .ci-name .cin-title {
  font-weight: 600; }

.cart-box .cart-item .ci-name .cin-price {
  color: #d71149; }

.cart-box .cart-item .ci-price {
  width: 105px;
  position: absolute;
  right: 0px;
  text-align: right;
  top: 0px; }

.cart-box .cart-item .qty-total-price {
  background: transparent;
  padding: 14px 10px 0px 0px; }

.cart-box .cart-item .qty-total-price .quantity input {
  border-top: 0px solid #d8d8d8;
  border-bottom: 0px solid #d8d8d8;
  height: 25px;
  padding: 0px 0px;
  text-align: center; }

.cart-box .cart-item .qty-total-price .quantity-button {
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px;
  height: 25px;
  width: 25px;
  padding: 2px 0px;
  line-height: normal;
  font-size: 14px;
  border: 1px solid #d71149; }

.checkout-payable {
  margin: 20px 0px;
  background-color: #f9f9f9;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
  padding: 15px; }

.checkout-payable .cart-cp {
  position: relative; }

.checkout-payable .cp-left {
  padding-right: 140px;
  padding-bottom: 5px; }

.checkout-payable .cp-right {
  position: absolute;
  right: 0px;
  width: 140px;
  text-align: right;
  top: 0px; }

.checkout-payable .cart-coupon-discount .cp-right {
  color: #d71149;
  font-style: italic;
  font-size: 12px; }

.checkout-payable .cart-total-payable {
  margin-top: 10px;
  padding-top: 15px;
  border-top: 1px solid #e1e1e1; }

.checkout-payable .cart-total-payable .cp-right {
  font-weight: 600;
  color: #d71149;
  padding-top: 15px; }

.checkout-payable .cart-total-payable .cp-left {
  font-weight: 600;
  color: #d71149; }

.button-add-cart {
  margin: 30px auto 0px;
  width: 150px;
  display: block;
  font-weight: 600;
  position: relative;
  text-align: left;
  padding: 0px 40px 0px 23px;
  -webkit-border-radius: 90px;
  -moz-border-radius: 90px;
  border-radius: 90px; }

.button-add-cart .fa,
.button-add-cart .fas,
.button-add-cart .far,
.button-add-cart .fal,
.button-add-cart .fab {
  position: absolute;
  top: 0px;
  right: 17px; }

/*==================================================
9. CHECKOUT
==================================================*/
.shipping-checkout-page .checkout-payable {
  border: 1px solid #e0e0e0;
  margin: 0px 0px; }

.shipping-checkout-page .payment-method-text {
  font-weight: 600;
  margin: 0px 0px 10px; }

.shipping-checkout-page .payment-method-text .fa,
.shipping-checkout-page .payment-method-text .fas,
.shipping-checkout-page .payment-method-text .far,
.shipping-checkout-page .payment-method-text .fal,
.shipping-checkout-page .payment-method-text .fab {
  color: #d71149;
  margin-right: 5px; }

.shipping-checkout-page .ck-box {
  padding: 10px 20px 20px;
  background: #fbfbfb;
  border: 1px solid #e0e0e0;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px; }

.shipping-checkout-page .label-checkbox {
  padding-left: 30px; }

.shipping-checkout-page [type="checkbox"].filled-in:not(:checked) + label:after,
.shipping-checkout-page [type="checkbox"].filled-in:checked + label::after,
[type="checkbox"].filled-in:checked + label::before {
  top: 5px; }

/*==================================================
10. WISH LIST
==================================================*/
.wish-item {
  padding: 0px 0px 0px;
  margin: 0px 0px; }

.wishlist-page {
  padding: 20px 0px 30px; }

.box-wish-list {
  margin: 0px 0px; }

.wish-item .wish-box {
  width: 100%;
  position: relative;
  min-height: 70px;
  border-bottom: 1px solid #f2f2f2; }

.wish-item .wish-box .wi-img {
  width: 70px;
  position: absolute;
  left: 0px;
  top: 0px; }

.wish-item .wish-box .wi-img .wi-img-product {
  width: 100%;
  height: 70px;
  position: relative;
  background-size: 70% auto;
  background-position: center center;
  background-repeat: no-repeat; }

.wish-item .wish-box .wi-remove {
  width: 40px;
  position: absolute;
  right: 10px;
  text-align: right;
  top: 0px;
  padding: 20px 0px; }

.wish-item .wish-box .wi-remove .fa,
.wish-item .wish-box .wi-remove .fas,
.wish-item .wish-box .wi-remove .far,
.wish-item .wish-box .wi-remove .fal,
.wish-item .wish-box .wi-remove .fab {
  font-size: 30px; }

.wish-item .wish-box .wi-name {
  padding-left: 70px;
  height: 100%;
  padding-right: 40px; }

.wish-item .wish-box .wi-name .win-top {
  padding: 15px 10px; }

.wish-item .wish-box .wi-name .win-title {
  font-weight: 600; }

.wish-item .wish-box .wi-name .win-price {
  color: #d71149; }

.wish-item .wish-box .wi-name .win-info {
  font-size: 12px;
  line-height: 1.2; }

/*==================================================
11. CONTACT
==================================================*/
.contact-wrap.theme-form {
  padding: 30px 20px 30px; }

/*==================================================
12. PROMO
==================================================*/
.section.promo {
  margin-top: 10px; }

.section.promo img {
  max-width: 100%;
  width: 100%;
  height: auto; }

/*==================================================
13. SUBSCRIBE
==================================================*/
.section.subscribe {
  position: relative;
  padding-top: 20px;
  padding-bottom: 20px; }

.section.subscribe .bg-subscribe {
  position: absolute;
  top: 0px;
  left: 0px;
  background-size: 100% auto;
  z-index: -1;
  height: 100%;
  width: 100%;
  opacity: 0.2; }

.section.subscribe .mail-subscribe-box {
  position: relative;
  padding-bottom: 35px;
  margin-top: 20px;
  padding: 0px 20px; }

.section.subscribe .mail-subscribe-box input {
  color: #303030;
  border: 1px solid #d71149;
  height: 50px;
  background: #ffffff;
  border-radius: 0;
  margin-bottom: 0px;
  padding: 10px 50px 10px 20px;
  box-sizing: border-box; }

.section.subscribe .mail-subscribe-box i {
  position: absolute;
  color: #d71149;
  font-size: 18px;
  border: 1px solid #d71149;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  padding: 4px 8px 4px 10px;
  right: 30px;
  top: 11px;
  cursor: pointer;
  width: 28px;
  height: 28px; }

#page-content #subscribe-page {
  bottom: -2px; }

/*==================================================
14. SETTING
==================================================*/
.setting-page {
  padding: 30px 20px 50px; }

form .file-field.input-field {
  overflow: hidden; }

form .file-field.input-field .btn {
  padding: 0px 20px;
  line-height: normal;
  height: auto;
  -webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
  -moz-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0); }

form .file-field .file-path-wrapper .file-path {
  height: auto;
  margin-top: 25px; }

.setting-page .sphoto-text {
  font-size: 12px;
  color: #9e9e9e;
  padding-bottom: 10px; }

.setting-page .setting-photo {
  text-align: center;
  height: 130px;
  width: 130px; }

.setting-page .setting-photo img {
  max-width: 100%;
  max-height: 100%; }

/*==================================================
15. GALLERY
==================================================*/
.section.gallery .wrap-gallery img {
  width: 100%;
  height: 100%;
  float: left; }

.section.gallery .col {
  padding: 3px;
  margin: 0; }

.section.gallery .filter-gallery {
  text-align: center; }

.section.gallery .filter-button {
  color: #d71149;
  background: transparent;
  border: 0px solid #ffffff;
  padding: 10px 10px;
  font-weight: 600;
  margin-bottom: 5px;
  border: 1px solid #cecece; }

.section.gallery .filter-button.active {
  color: #ff8700; }

.section.gallery .gallery-img-box,
.section.gallery .gallery-img-box * {
  -webkit-transition: all 0.5s ease-in-out;
  -moz-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  -ms-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out; }

.section.gallery .gallery-img-box.gallery-hidden {
  display: none; }

/*==================================================
16. TESTIMONIAL
==================================================*/
.section.testimonial {
  padding-top: 20px;
  position: relative;
  padding-bottom: 10px; }

.section.testimonial .testimonial-wrap {
  position: relative;
  z-index: 2; }

.section.testimonial .section-title {
  border-bottom: 0px solid #e0e0e0;
  padding-bottom: 0px; }

.section.testimonial .background-testimonial {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-size: auto 100%;
  opacity: 0.2; }

.section.testimonial .wrap-item-testimonial .item-testimonial {
  width: 100%;
  height: auto;
  text-align: center;
  position: relative; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-info {
  width: 100%;
  text-align: center;
  position: relative; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-info .client-img {
  height: 100px;
  width: 100px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  overflow: hidden;
  margin: 0 auto 10px; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-info .client-img img {
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-info .rating {
  color: #ffc700; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-content {
  padding: 0px 0px 0px 0px; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-content p {
  font-style: italic;
  padding: 0px 20px; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-content .client-title h4 {
  font-size: 18px;
  margin: 20px 0px 0px;
  font-weight: 600; }

.section.testimonial .wrap-item-testimonial .item-testimonial .client-content .client-title h5 {
  font-weight: 300;
  font-size: 14px; }

/*==================================================
17. 404 PAGE
==================================================*/
.error-page {
  text-align: center;
  width: 100%;
  padding-bottom: 100%;
  height: 0px;
  position: relative; }

.error-page h1 {
  margin: 0px 0px;
  font-size: 58px;
  font-weight: 600; }

.error-page .in-error-page {
  width: 100%;
  height: 100%;
  position: absolute;
  display: table; }

.error-page .in-error-page .in-in-error-page {
  display: table-cell;
  vertical-align: middle;
  text-align: center; }

/*==================================================
18. LOGIN & REGISTER FORM
==================================================*/
.login-form,
.register-form {
  padding: 30px 20px 30px; }

.login-form input {
  margin-bottom: 0px; }

.login-form .forgot-password-link {
  text-align: center;
  margin: 20px 0px; }

.login-form .forgot-password-link a:hover {
  cursor: pointer; }

.login-form .or-line {
  position: relative; }

.login-form .or-line .ol-or {
  display: block;
  text-align: center;
  background: #ffffff;
  width: 40px;
  margin: 0 auto;
  color: #9e9e9e;
  position: relative;
  z-index: 1;
  font-size: 12px; }

.login-form .or-line .ol-line {
  height: 1px;
  width: 100%;
  background-color: #9e9e9e;
  position: absolute;
  top: 10px;
  left: 0px;
  z-index: 0; }

.login-form .section-title,
.register-form .section-title {
  border-bottom: 0px solid #e0e0e0;
  padding-bottom: 0px; }

.login-form .sign-in-sosmed {
  width: 170px;
  margin: 0px auto 0px; }

.login-form .row.fp-text {
  margin-bottom: 0px; }

.login-form .sign-in-sosmed .sign-in-icon {
  width: 45px;
  height: 45px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  display: table;
  color: #ffffff;
  text-align: center;
  float: left;
  margin: 0px 20px;
  cursor: pointer; }

.login-form .sign-in-sosmed .sign-in-icon .fa,
.login-form .sign-in-sosmed .sign-in-icon .fab,
.login-form .sign-in-sosmed .sign-in-icon .fas,
.login-form .sign-in-sosmed .sign-in-icon .fal,
.login-form .sign-in-sosmed .sign-in-icon .far {
  display: table-cell;
  vertical-align: middle;
  font-size: 15px; }

.login-form .sign-in-sosmed .sign-in-icon.sii-twitter {
  background-color: #00bbed; }

.login-form .sign-in-sosmed .sign-in-icon.sii-facebook {
  background-color: #2f7abc; }

/*==================================================
19. PRODUCT PAGE
==================================================*/
.pg-product-image {
  overflow: hidden; }

.pg-product-image .slick-slide {
  margin: 0 10px; }

.pg-product-image .slick-list {
  margin: 0 -10px; }

.pg-product-image .slick-dots {
  bottom: 0px; }

.pg-product-image .slick-dots li button::before {
  font-size: 10px; }

.pg-product-image .slick-dots li {
  margin: 0px 0px; }

.pg-product-image {
  position: relative;
  margin-bottom: 0px !important; }

.pg-product-image {
  height: 200px;
  overflow: hidden; }

.pg-product-image .pgp-wrap-img .pgp-wrap-img-in {
  height: 200px;
  width: 100%;
  background-color: #fff; }

.pg-product-image .pgp-wrap-img .pgp-wrap-img-in .pgp-img {
  height: 100%;
  background-position: center center;
  background-size: auto 100%;
  background-repeat: no-repeat; }

.add-wish-lish .awl-btn {
  width: 45px;
  height: 45px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  display: table;
  background-color: #ffffff;
  border: 1px solid #f8f8f8;
  margin-top: -30px;
  position: relative;
  float: right;
  -webkit-box-shadow: 2px 2px 5px 0px rgba(50, 50, 50, 0.21);
  -moz-box-shadow: 2px 2px 5px 0px rgba(50, 50, 50, 0.21);
  box-shadow: 2px 2px 5px 0px rgba(50, 50, 50, 0.21);
  cursor: pointer; }

.add-wish-lish .awl-btn:hover {
  background-color: #d71149;
  border: 1px solid #d71149; }

.add-wish-lish .awl-btn:hover .awl-btn-icon {
  color: #ffffff; }

.add-wish-lish .awl-btn .awl-btn-icon {
  display: table-cell;
  vertical-align: middle;
  font-size: 18px;
  text-align: center;
  color: #d71149; }

.product-page .name-price {
  position: relative; }

.product-page .pg-product-name {
  font-size: 20px;
  color: #d71149;
  width: 100%;
  padding-right: 100px; }

.product-page .pg-product-price {
  font-size: 20px;
  font-weight: 600;
  color: #ff8700;
  float: right;
  width: 100px;
  text-align: right;
  padding-right: 5px;
  position: absolute;
  right: 0px;
  top: 0px; }

.product-page .desciption-product {
  margin-bottom: 50px; }

.qty-total-price .quantity {
  background: #ffffff;
  position: relative;
  height: 42px; }

.qty-total-price input[type=number]::-webkit-inner-spin-button,
.qty-total-price input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0; }

.qty-total-price input[type=number] {
  -moz-appearance: textfield; }

.qty-total-price {
  background-color: #f6f6f6;
  padding: 20px 0px; }

.qty-total-price .quantity input {
  border-top: 1px solid #d8d8d8;
  border-bottom: 1px solid #d8d8d8;
  height: 42px;
  line-height: 1.65;
  display: block;
  padding: 0;
  margin: 0;
  left: 25%;
  width: 50%;
  padding: 0px 10px;
  text-align: center;
  position: absolute;
  box-sizing: border-box; }

.qty-total-price .row {
  margin-bottom: 0px; }

.qty-total-price .quantity input:focus {
  outline: 0; }

.qty-total-price .quantity-nav {
  float: left;
  position: relative;
  height: 42px; }

.qty-total-price .quantity-button {
  position: absolute;
  padding: 5px 0px;
  cursor: pointer;
  width: 25%;
  text-align: center;
  color: #333;
  font-size: 18px;
  line-height: 1.7;
  border: 1px solid #d8d8d8;
  height: 42px;
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none; }

.qty-total-price .quantity-button.quantity-up {
  position: absolute;
  top: 0;
  right: -25%; }

.qty-total-price .quantity-button.quantity-down {
  position: absolute;
  top: 0;
  left: 25%; }

.qty-total-price .qty-qty {
  padding: 10px 5px 0px;
  color: #d71149; }

.qty-total-price .button-add-cart {
  margin: 0px 0px;
  padding: 0px 0px;
  text-align: center;
  font-weight: 600;
  width: 100%;
  height: 42px;
  box-sizing: border-box;
  -webkit-box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0px 0px 0px rgba(0, 0, 0, 0);
  box-shadow: 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0px 0px 0 rgba(0, 0, 0, 0), 0 0px 0px 0px rgba(0, 0, 0, 0); }

/*==================================================
20. FOOTER
==================================================*/
footer {
  background-color: #d71149; }

footer .footer-info {
  color: #ffffff;
  border-bottom: 1px solid #c30f42;
  padding: 35px 20px 35px; }

footer .fas {
  color: #ff8700; }

footer#footer .row-footer-icon {
  margin-bottom: 0px; }

footer#footer .footer-sosmed-icon {
  text-align: center;
  margin-top: 20px; }

footer#footer .footer-sosmed-icon .wrap-circle-sosmed {
  display: inline-block;
  margin-right: 5px; }

footer#footer .footer-sosmed-icon .wrap-circle-sosmed .circle-sosmed {
  width: 35px;
  height: 35px;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  border-radius: 100%;
  display: table;
  background-color: #ffffff;
  border: 1px solid #d71149; }

footer#footer .footer-sosmed-icon .wrap-circle-sosmed .circle-sosmed .fa,
footer#footer .footer-sosmed-icon .wrap-circle-sosmed .circle-sosmed .fab,
footer#footer .footer-sosmed-icon .wrap-circle-sosmed .circle-sosmed .fas,
footer#footer .footer-sosmed-icon .wrap-circle-sosmed .circle-sosmed .far,
footer#footer .footer-sosmed-icon .wrap-circle-sosmed .circle-sosmed .fal {
  display: table-cell;
  vertical-align: middle;
  font-size: 15px; }

footer#footer .footer-sosmed-icon .circle-sosmed .fas {
  font-size: 16px;
  display: table-cell;
  text-align: center;
  vertical-align: middle; }

footer#footer .row.copyright {
  text-align: center;
  text-align: center;
  margin: 0px 0px 0px;
  padding: 10px 0px 20px;
  color: #ffffff; }

footer#footer .row.copyright span {
  font-weight: 600; }

 
