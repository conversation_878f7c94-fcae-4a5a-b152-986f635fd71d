/**
 * PRODUTOS MODERN JS - Substituto das funcionalidades do Materialize
 * Sistema moderno de accordion, modais e interações
 */

class ModernUI {
    constructor() {
        this.init();
    }

    init() {
        this.initAccordion();
        this.initModals();
        this.initToasts();
        this.initFormValidation();
        console.log('🎨 Modern UI inicializado com sucesso!');
    }

    // ===== ACCORDION SYSTEM =====
    initAccordion() {
        const accordionHeaders = document.querySelectorAll('.accordion-header');
        
        accordionHeaders.forEach(header => {
            header.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleAccordion(header);
            });
        });

        // Abrir primeira seção por padrão
        if (accordionHeaders.length > 0) {
            this.openAccordion(accordionHeaders[0]);
        }
    }

    toggleAccordion(header) {
        const content = header.nextElementSibling;
        const isActive = header.classList.contains('active');

        if (isActive) {
            this.closeAccordion(header);
        } else {
            // Fechar outros accordions (comportamento exclusivo)
            document.querySelectorAll('.accordion-header.active').forEach(activeHeader => {
                if (activeHeader !== header) {
                    this.closeAccordion(activeHeader);
                }
            });
            this.openAccordion(header);
        }
    }

    openAccordion(header) {
        const content = header.nextElementSibling;
        header.classList.add('active');
        content.classList.add('active');
        
        // Scroll suave para a seção
        setTimeout(() => {
            header.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }, 100);
    }

    closeAccordion(header) {
        const content = header.nextElementSibling;
        header.classList.remove('active');
        content.classList.remove('active');
    }

    // ===== MODAL SYSTEM =====
    initModals() {
        // Fechar modal ao clicar no overlay
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target.querySelector('.modal'));
            }
        });

        // Fechar modal com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.active');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        // Criar overlay se não existir
        let overlay = modal.closest('.modal-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'modal-overlay';
            overlay.appendChild(modal);
            document.body.appendChild(overlay);
        }

        overlay.style.display = 'flex';
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Focar no primeiro input
        setTimeout(() => {
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    closeModal(modal) {
        if (!modal) return;
        
        const overlay = modal.closest('.modal-overlay');
        modal.classList.remove('active');
        
        setTimeout(() => {
            if (overlay) {
                overlay.style.display = 'none';
            }
            document.body.style.overflow = '';
        }, 300);
    }

    // ===== TOAST SYSTEM =====
    initToasts() {
        // Criar container de toasts se não existir
        if (!document.getElementById('toast-container')) {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                display: flex;
                flex-direction: column;
                gap: 10px;
            `;
            document.body.appendChild(container);
        }
    }

    toast(message, duration = 4000, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };

        toast.style.cssText = `
            background: ${colors[type] || colors.info};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
            word-wrap: break-word;
        `;
        
        toast.innerHTML = message;
        container.appendChild(toast);

        // Animar entrada
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);

        // Remover após duração
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    // ===== FORM VALIDATION =====
    initFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input[required], select[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
                
                input.addEventListener('input', () => {
                    if (input.classList.contains('error')) {
                        this.validateField(input);
                    }
                });
            });
        });
    }

    validateField(field) {
        const isValid = field.checkValidity();
        
        if (isValid) {
            field.classList.remove('error');
            this.removeFieldError(field);
        } else {
            field.classList.add('error');
            this.showFieldError(field);
        }
        
        return isValid;
    }

    showFieldError(field) {
        this.removeFieldError(field); // Remove erro anterior
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = `
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
        `;
        errorDiv.textContent = field.validationMessage;
        
        field.parentNode.appendChild(errorDiv);
    }

    removeFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    // ===== UTILITY METHODS =====
    showLoading(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (!element) return;

        element.style.position = 'relative';
        element.style.pointerEvents = 'none';
        
        const loader = document.createElement('div');
        loader.className = 'loading-overlay';
        loader.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        `;
        
        loader.innerHTML = '<div class="loading"></div>';
        element.appendChild(loader);
    }

    hideLoading(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (!element) return;

        const loader = element.querySelector('.loading-overlay');
        if (loader) {
            loader.remove();
        }
        element.style.pointerEvents = '';
    }

    // ===== COMPATIBILITY METHODS (para manter compatibilidade com código existente) =====
    
    // Substituto do Materialize.toast
    static toast(message, duration, className) {
        const ui = window.modernUI || new ModernUI();
        let type = 'info';
        
        if (className && className.includes('green')) type = 'success';
        else if (className && className.includes('red')) type = 'error';
        else if (className && className.includes('orange')) type = 'warning';
        
        ui.toast(message, duration, type);
    }
}

// ===== ESTILOS CSS ADICIONAIS PARA COMPONENTES =====
const additionalStyles = `
<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal {
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.7);
    opacity: 0;
    transition: all 0.3s ease;
}

.modal.active {
    transform: scale(1);
    opacity: 1;
}

.modal-content {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.form-input.error,
.form-select.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-error {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
}

/* Tabs modernos */
.modern-tabs {
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 20px;
}

.modern-tabs .tab-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    overflow-x: auto;
}

.modern-tabs .tab-item {
    flex-shrink: 0;
}

.modern-tabs .tab-link {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #6b7280;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.modern-tabs .tab-link:hover {
    color: #374151;
    background: #f9fafb;
}

.modern-tabs .tab-link.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}
</style>
`;

// Injetar estilos adicionais
document.head.insertAdjacentHTML('beforeend', additionalStyles);

// ===== INICIALIZAÇÃO =====
document.addEventListener('DOMContentLoaded', () => {
    window.modernUI = new ModernUI();
    
    // Compatibilidade com Materialize
    window.Materialize = {
        toast: ModernUI.toast
    };
});

// ===== FUNÇÕES GLOBAIS PARA COMPATIBILIDADE =====
function openModal(modalId) {
    window.modernUI.openModal(modalId);
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    window.modernUI.closeModal(modal);
}

function showToast(message, duration = 4000, type = 'info') {
    window.modernUI.toast(message, duration, type);
}

// ===== SCRIPT DE CONVERSÃO AUTOMÁTICA =====
function convertMaterializeToModern() {
    console.log('🔄 Iniciando conversão automática do Materialize para Modern UI...');

    let conversions = 0;

    // 1. Converter collapsible para accordion
    const collapsibles = document.querySelectorAll('ul.collapsible');
    collapsibles.forEach(ul => {
        const accordion = document.createElement('div');
        accordion.className = 'modern-accordion';

        const items = ul.querySelectorAll('li');
        items.forEach(li => {
            const item = document.createElement('div');
            item.className = 'accordion-item';

            const header = li.querySelector('.collapsible-header');
            const body = li.querySelector('.collapsible-body');

            if (header) {
                const newHeader = document.createElement('div');
                newHeader.className = 'accordion-header';

                const icon = header.querySelector('.material-icons');
                const text = header.textContent.replace(icon ? icon.textContent : '', '').trim();

                newHeader.innerHTML = `
                    <i class="material-icons icon">${icon ? icon.textContent : 'info'}</i>
                    <span class="title">${text}</span>
                    <i class="material-icons chevron">expand_more</i>
                `;

                item.appendChild(newHeader);
            }

            if (body) {
                const newBody = document.createElement('div');
                newBody.className = 'accordion-content';
                newBody.innerHTML = body.innerHTML;
                item.appendChild(newBody);
            }

            accordion.appendChild(item);
        });

        ul.parentNode.replaceChild(accordion, ul);
        conversions++;
    });

    // 2. Converter grid system
    const gridElements = document.querySelectorAll('[class*="col s"], [class*="col l"], [class*="col m"]');
    gridElements.forEach(el => {
        let classes = el.className;

        // Converter col s12 -> col col-12, etc.
        classes = classes.replace(/col\s+s(\d+)/g, 'col col-$1');
        classes = classes.replace(/col\s+l(\d+)/g, 'col col-$1');
        classes = classes.replace(/col\s+m(\d+)/g, 'col col-$1');

        // Remover classes específicas do Materialize
        classes = classes.replace(/\s*input-field\s*/g, ' ');

        el.className = classes.trim();
        conversions++;
    });

    // 3. Converter input-field para form-group
    const inputFields = document.querySelectorAll('.input-field');
    inputFields.forEach(field => {
        field.classList.remove('input-field');
        field.classList.add('form-group');

        const input = field.querySelector('input, select, textarea');
        const label = field.querySelector('label');

        if (input) {
            input.classList.remove('input-field');
            if (input.tagName === 'SELECT') {
                input.classList.add('form-select');
            } else {
                input.classList.add('form-input');
            }
        }

        if (label) {
            label.classList.add('form-label');
            label.classList.remove('active');
        }

        conversions++;
    });

    // 4. Converter botões
    const buttons = document.querySelectorAll('.btn:not(.btn-primary):not(.btn-success):not(.btn-danger):not(.btn-secondary)');
    buttons.forEach(btn => {
        // Remover classes do Materialize
        btn.classList.remove('waves-effect', 'waves-light', 'waves-green', 'waves-red');

        // Adicionar classes modernas baseadas na cor
        if (btn.classList.contains('green') || btn.textContent.toLowerCase().includes('gravar') || btn.textContent.toLowerCase().includes('salvar')) {
            btn.classList.add('btn-success');
            btn.classList.remove('green');
        } else if (btn.classList.contains('red') || btn.textContent.toLowerCase().includes('excluir') || btn.textContent.toLowerCase().includes('deletar')) {
            btn.classList.add('btn-danger');
            btn.classList.remove('red');
        } else if (btn.classList.contains('blue') || btn.classList.contains('indigo')) {
            btn.classList.add('btn-primary');
            btn.classList.remove('blue', 'indigo', 'lighten-2');
        } else {
            btn.classList.add('btn-secondary');
        }

        conversions++;
    });

    // 5. Converter tabelas
    const tables = document.querySelectorAll('table.responsive-table, table.striped');
    tables.forEach(table => {
        table.classList.remove('responsive-table', 'striped');
        table.classList.add('modern-table');
        conversions++;
    });

    // 6. Converter checkboxes
    const checkboxContainers = document.querySelectorAll('p:has(input[type="checkbox"]), div:has(input[type="checkbox"])');
    checkboxContainers.forEach(container => {
        if (container.querySelector('input[type="checkbox"]') && container.querySelector('label')) {
            container.classList.add('form-checkbox');
            conversions++;
        }
    });

    console.log(`✅ Conversão concluída! ${conversions} elementos convertidos.`);
    console.log('🎨 Reinicializando Modern UI...');

    // Reinicializar o Modern UI
    window.modernUI = new ModernUI();

    return conversions;
}
