/* ===== PRODUTOS MODERN CSS - Substituto do Materialize ===== */

/* Reset e Base */
* {
    box-sizing: border-box;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== ACCORDION/COLLAPSIBLE MODERNO ===== */
.modern-accordion {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.accordion-item {
    border-bottom: 1px solid #e0e0e0;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    user-select: none;
}

.accordion-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.accordion-header.active {
    background: linear-gradient(135deg, #4f5bd5 0%, #5e3a7e 100%);
}

.accordion-header .icon {
    font-size: 24px;
    margin-right: 12px;
}

.accordion-header .title {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
}

.accordion-header .chevron {
    font-size: 20px;
    transition: transform 0.3s ease;
}

.accordion-header.active .chevron {
    transform: rotate(180deg);
}

.accordion-content {
    background: white;
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.accordion-content.active {
    max-height: 2000px;
    padding: 24px;
}

/* ===== GRID SYSTEM MODERNO ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
}

.col {
    padding: 8px;
    flex: 1;
}

.col-1 { flex: 0 0 8.333%; max-width: 8.333%; }
.col-2 { flex: 0 0 16.666%; max-width: 16.666%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333%; max-width: 33.333%; }
.col-5 { flex: 0 0 41.666%; max-width: 41.666%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333%; max-width: 58.333%; }
.col-8 { flex: 0 0 66.666%; max-width: 66.666%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333%; max-width: 83.333%; }
.col-11 { flex: 0 0 91.666%; max-width: 91.666%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Responsivo */
@media (max-width: 768px) {
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-4 { flex: 0 0 33.333%; max-width: 33.333%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
}

/* ===== FORM FIELDS MODERNOS ===== */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input:invalid {
    border-color: #ef4444;
}

.form-input:invalid:focus {
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Select moderno */
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Checkbox moderno */
.form-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.form-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    accent-color: #667eea;
}

.form-checkbox label {
    font-size: 14px;
    color: #374151;
    cursor: pointer;
}

/* ===== BOTÕES MODERNOS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
    transform: translateY(-1px);
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
}

/* ===== TABELAS MODERNAS ===== */
.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.modern-table th {
    background: #f8fafc;
    padding: 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.modern-table td {
    padding: 16px;
    border-bottom: 1px solid #f3f4f6;
}

.modern-table tr:hover {
    background: #f8fafc;
}

/* ===== CARDS MODERNOS ===== */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.card-header {
    padding: 20px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.card-content {
    padding: 20px;
}

/* ===== UTILITÁRIOS ===== */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-center { justify-content: center; }
.justify-content-end { justify-content: flex-end; }

.gap-1 { gap: 8px; }
.gap-2 { gap: 16px; }
.gap-3 { gap: 24px; }

/* ===== LOADING E ESTADOS ===== */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden { display: none !important; }
.visible { display: block !important; }

/* ===== RESPONSIVIDADE ===== */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    
    .accordion-content.active {
        padding: 16px;
    }
    
    .row {
        margin: -4px;
    }
    
    .col {
        padding: 4px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 13px;
    }
}
