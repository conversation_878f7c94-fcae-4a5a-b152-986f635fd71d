<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>SetLink</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetLink</h1>
<code>SetLink(<b>int</b> link [, <b>float</b> y [, <b>int</b> page]])</code>
<h2>Description</h2>
Defines the page and position a link points to.
<h2>Parameters</h2>
<dl class="param">
<dt><code>link</code></dt>
<dd>
The link identifier returned by AddLink().
</dd>
<dt><code>y</code></dt>
<dd>
Ordinate of target position; <code>-1</code> indicates the current position.
The default value is <code>0</code> (top of page).
</dd>
<dt><code>page</code></dt>
<dd>
Number of target page; <code>-1</code> indicates the current page. This is the default value.
</dd>
</dl>
<h2>See also</h2>
<a href="addlink.htm">AddLink</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
