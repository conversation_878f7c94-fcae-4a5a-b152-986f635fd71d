/**
 * PRODUTOS VISUAL MELHORADO - Melhorias visuais simples
 * Trabalha EM CONJUNTO com Materialize (não substitui)
 */

// Função para melhorar visual da edição de produtos
function melhorarVisualEdicao() {
    console.log('🎨 Aplicando melhorias visuais...');
    
    const edicaoSection = document.getElementById('produto_cadastro');
    if (!edicaoSection) {
        console.log('ℹ️ Seção de edição não encontrada');
        return;
    }
    
    // Aplicar estilo moderno à seção
    edicaoSection.style.fontFamily = "'Inter', sans-serif";
    edicaoSection.style.background = "#f8fafc";
    edicaoSection.style.padding = "20px";
    
    // Melhorar headers do collapsible
    const headers = edicaoSection.querySelectorAll('.collapsible-header');
    headers.forEach((header, index) => {
        header.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        header.style.borderRadius = '8px 8px 0 0';
        header.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        header.style.marginTop = index > 0 ? '16px' : '0';
    });
    
    // Melhorar bodies do collapsible
    const bodies = edicaoSection.querySelectorAll('.collapsible-body');
    bodies.forEach(body => {
        body.style.background = '#ffffff';
        body.style.borderRadius = '0 0 8px 8px';
        body.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        body.style.border = '1px solid #e5e7eb';
        body.style.borderTop = 'none';
    });
    
    // Melhorar inputs
    const inputs = edicaoSection.querySelectorAll('input[type="text"], input[type="date"], select');
    inputs.forEach(input => {
        input.style.borderRadius = '6px';
        input.style.border = '1px solid #d1d5db';
        input.style.padding = '8px 12px';
        input.style.fontSize = '14px';
        
        // Efeito focus
        input.addEventListener('focus', function() {
            this.style.borderColor = '#667eea';
            this.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
        });
        
        input.addEventListener('blur', function() {
            this.style.borderColor = '#d1d5db';
            this.style.boxShadow = 'none';
        });
    });
    
    // Melhorar botões
    const buttons = edicaoSection.querySelectorAll('.btn, .btn-floating');
    buttons.forEach(button => {
        button.style.borderRadius = '6px';
        button.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        button.style.transition = 'all 0.3s ease';
        
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        });
    });
    
    // Melhorar tabelas
    const tables = edicaoSection.querySelectorAll('table');
    tables.forEach(table => {
        table.style.borderRadius = '8px';
        table.style.overflow = 'hidden';
        table.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        
        // Headers da tabela
        const tableHeaders = table.querySelectorAll('th');
        tableHeaders.forEach(th => {
            th.style.background = '#f8fafc';
            th.style.fontWeight = '600';
            th.style.color = '#374151';
        });
        
        // Linhas da tabela
        const tableRows = table.querySelectorAll('tbody tr');
        tableRows.forEach((row, index) => {
            if (index % 2 === 0) {
                row.style.background = '#ffffff';
            } else {
                row.style.background = '#f9fafb';
            }
        });
    });
    
    console.log('✅ Melhorias visuais aplicadas com sucesso!');
    
    // Toast de sucesso
    if (typeof M !== 'undefined' && M.toast) {
        M.toast({html: '🎨 Visual melhorado!', displayLength: 2000});
    }
}

// Função para observar quando a seção de edição é mostrada
function observarEdicao() {
    const edicaoSection = document.getElementById('produto_cadastro');
    if (!edicaoSection) return;
    
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && 
                mutation.attributeName === 'style' && 
                edicaoSection.style.display !== 'none' &&
                !edicaoSection.dataset.melhorado) {
                
                // Marcar como melhorado para evitar aplicar múltiplas vezes
                edicaoSection.dataset.melhorado = 'true';
                
                // Aguardar um pouco para garantir que tudo carregou
                setTimeout(melhorarVisualEdicao, 500);
                
                // Parar de observar após aplicar
                observer.disconnect();
            }
        });
    });
    
    observer.observe(edicaoSection, { 
        attributes: true, 
        attributeFilter: ['style'] 
    });
}

// Disponibilizar globalmente
window.melhorarVisualEdicao = melhorarVisualEdicao;
window.observarEdicao = observarEdicao;

console.log('🎨 Sistema de melhorias visuais carregado!');
