{% if site.url == 'local' %}
  <!-- analytics removed -->
{% else %}
<!-- Analytics start -->
<script type="text/javascript">
  var _gaq = _gaq || [];
    _gaq.push(['_setAccount', '***********-1']);
    _gaq.push(['_trackPageview']);

    (function() {
      var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
      ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
      var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
    })();

    function recordOutboundLink(link, category, action) {
      _gat._getTrackerByName()._trackEvent(category, action);
      setTimeout('document.location = "' + link.href + '"', 100);
    }
</script>

<!-- ya metrika -->
<script type="text/javascript">(function (d, w, c) { (w[c] = w[c] || []).push(function() { try { w.yaCounter******** = new Ya.Metrika({id:********, webvisor:true, clickmap:true, trackLinks:true, accurateTrackBounce:true}); } catch(e) { } }); var n = d.getElementsByTagName("script")[0], s = d.createElement("script"), f = function () { n.parentNode.insertBefore(s, n); }; s.type = "text/javascript"; s.async = true; s.src = (d.location.protocol == "https:" ? "https:" : "http:") + "//mc.yandex.ru/metrika/watch.js"; if (w.opera == "[object Opera]") { d.addEventListener("DOMContentLoaded", f, false); } else { f(); } })(document, window, "yandex_metrika_callbacks");</script><noscript><div><img src="//mc.yandex.ru/watch/********" style="position:absolute; left:-9999px;" alt="" /></div></noscript>
{% endif %}