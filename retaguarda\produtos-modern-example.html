<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos - Edição Moderna</title>
    
    <!-- CSS Moderno -->
    <link rel="stylesheet" href="css/produtos-modern.css">
    
    <!-- Material Icons (mantemos apenas os ícones) -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Cabeçalho -->
        <div class="card mb-3">
            <div class="card-content">
                <h1 class="card-title d-flex align-items-center gap-2">
                    <i class="material-icons">inventory_2</i>
                    Edição de Produto
                </h1>
            </div>
        </div>

        <!-- Formulário Principal -->
        <form id="produto-form">
            <div class="modern-accordion">
                
                <!-- SEÇÃO 1: INFORMAÇÕES BÁSICAS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">info</i>
                        <span class="title">Informações Básicas</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <!-- Grupo 1: Identificação -->
                        <div class="row">
                            <div class="col col-4">
                                <div class="form-group">
                                    <label class="form-label" for="codigo_gtin">Código GTIN</label>
                                    <input type="text" id="codigo_gtin" class="form-input" placeholder="Digite o código GTIN" onfocusout="verificarCodigo();" required>
                                    <input type="hidden" id="codigo_interno" value="0">
                                </div>
                            </div>
                            <div class="col col-8">
                                <div class="form-group">
                                    <label class="form-label" for="descricao">Descrição</label>
                                    <input type="text" id="descricao" class="form-input" placeholder="Descrição do produto" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col col-12">
                                <div class="form-group">
                                    <label class="form-label" for="descricao_detalhada">Descrição Detalhada</label>
                                    <input type="text" id="descricao_detalhada" class="form-input" placeholder="Descrição detalhada do produto">
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 2: Classificação -->
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="grupo">Grupo</label>
                                    <select id="grupo" class="form-select">
                                        <option value="">Selecione um grupo</option>
                                        <option value="PRINCIPAL">PRINCIPAL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalGrupo')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="subgrupo">Sub Grupo</label>
                                    <select id="subgrupo" class="form-select">
                                        <option value="">Selecione um subgrupo</option>
                                        <option value="PRINCIPAL">PRINCIPAL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalSubGrupo')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="categoria">Categoria</label>
                                    <select id="categoria" class="form-select">
                                        <option value="">Selecione uma categoria</option>
                                        <option value="PRINCIPAL">PRINCIPAL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalCategoria')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 3: Unidade e Preços -->
                        <div class="row">
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="unidade">Unidade</label>
                                    <select id="unidade" class="form-select">
                                        <option value="">Selecione</option>
                                        <option value="UN">UN</option>
                                        <option value="KG">KG</option>
                                        <option value="LT">LT</option>
                                        <option value="MT">MT</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalUnidade')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="preco_venda">$ Venda</label>
                                    <input type="text" id="preco_venda" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="preco_compra">$ Compra</label>
                                    <input type="text" id="preco_compra" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="perc_lucro">% Lucro</label>
                                    <input type="text" id="perc_lucro" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-secondary btn-icon" title="Calcular">
                                        <i class="material-icons">calculate</i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 4: Tributação -->
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="ncm">NCM</label>
                                    <input type="text" id="ncm" class="form-input" placeholder="NCM">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="cest">CEST</label>
                                    <input type="text" id="cest" class="form-input" placeholder="CEST">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="cfop">CFOP</label>
                                    <input type="text" id="cfop" class="form-input" value="5102">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="situacao_tributaria">Situação Tributária</label>
                                    <select id="situacao_tributaria" class="form-select">
                                        <option value="" disabled selected>Selecione</option>
                                        <option value="101">101 - Tributação com permissão de crédito</option>
                                        <option value="102" selected>102 - Tributação sem permissão de crédito</option>
                                        <option value="103">103 - Isenção do ICMS para faixa de receita bruta</option>
                                        <option value="201">201 - Tributada COM permissão de crédito e com cobrança do ICMS POR ST</option>
                                        <option value="202">202 - Tributada SEM permissão de crédito e com cobrança do ICMS POR ST</option>
                                        <option value="203">203 - Isenção do ICMS para faixa de receita bruta e com cobrança do ICMS POR ST</option>
                                        <option value="300">300 - Imune</option>
                                        <option value="500">500 - ICMS cobrado anteriormente por ST ou por antecipação</option>
                                        <option value="900">900 - Outros</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 5: ICMS e Configurações -->
                        <div class="row">
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="perc_icms">% ICMS</label>
                                    <input type="text" id="perc_icms" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-checkbox">
                                    <input type="checkbox" id="produto_balanca">
                                    <label for="produto_balanca">Prod.Balança</label>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="vadidade">Validade</label>
                                    <input type="text" id="vadidade" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="data_cadastro">Dt Cadastro</label>
                                    <input type="text" id="data_cadastro" class="form-input" disabled>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="data_alteracao">Dt Alteração</label>
                                    <input type="text" id="data_alteracao" class="form-input" disabled>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 6: Fornecedor -->
                        <div class="row">
                            <div class="col col-7">
                                <div class="form-group">
                                    <label class="form-label" for="fornecedor">Fornecedor</label>
                                    <select id="fornecedor" class="form-select" onchange="selecionouFornecedor()">
                                        <option value="">Selecione um fornecedor</option>
                                        <option value="0">Nenhum</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalFornecedor')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-4">
                                <div class="form-checkbox">
                                    <input type="checkbox" id="vender_ecomerce">
                                    <label for="vender_ecomerce">Vender no E-commerce</label>
                                </div>
                                <div class="form-checkbox">
                                    <input type="checkbox" id="produto_producao">
                                    <label for="produto_producao">Produto de Produção</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEÇÃO 2: COMPOSIÇÃO -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">hive</i>
                        <span class="title">Composição</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <!-- Campos de entrada para composição -->
                        <div class="row">
                            <div class="col col-4">
                                <div class="form-group">
                                    <label class="form-label" for="codigo_gtin_composicao">
                                        <i class="material-icons" style="vertical-align: middle; margin-right: 5px; font-size: 18px;">key</i>
                                        Código
                                    </label>
                                    <input type="text" id="codigo_gtin_composicao" class="form-input" placeholder="Digite o código do produto">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="descricao_composicao">Descrição</label>
                                    <input type="text" id="descricao_composicao" class="form-input" placeholder="Descrição do produto" readonly>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="qtde_composicao">Quantidade</label>
                                    <input type="number" id="qtde_composicao" class="form-input" step="0.01" min="0" placeholder="0.00">
                                </div>
                            </div>
                        </div>

                        <!-- Botão para adicionar item -->
                        <div class="row mb-3">
                            <div class="col col-12 text-center">
                                <button type="button" class="btn btn-success" onclick="adicionarItemComposicao()">
                                    <i class="material-icons">add</i>
                                    Adicionar à Composição
                                </button>
                            </div>
                        </div>

                        <!-- Tabela de itens da composição -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Itens da Composição</h6>
                            </div>
                            <div class="card-content" style="padding: 0;">
                                <table class="modern-table" id="userTableComposicao">
                                    <thead>
                                        <tr>
                                            <th>Código</th>
                                            <th>Descrição</th>
                                            <th>Quantidade</th>
                                            <th style="width: 100px;">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody id="composicao-table-body">
                                        <!-- Exemplo de item (será removido dinamicamente) -->
                                        <tr class="exemplo-item">
                                            <td colspan="4" style="text-align: center; color: #999; font-style: italic;">
                                                Nenhum item adicionado à composição
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Informações adicionais -->
                        <div class="row mt-3">
                            <div class="col col-12">
                                <div style="background: #f8fafc; padding: 16px; border-radius: 8px; border-left: 4px solid #667eea;">
                                    <p style="margin: 0; color: #374151; font-size: 14px;">
                                        <i class="material-icons" style="vertical-align: middle; margin-right: 8px; font-size: 18px; color: #667eea;">info</i>
                                        <strong>Composição:</strong> Adicione os produtos que compõem este item.
                                        Digite o código do produto e a quantidade necessária.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEÇÃO 3: OUTROS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">tune</i>
                        <span class="title">Outros</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">

                        <!-- GRUPO 1: CLASSIFICAÇÃO CLIENTE -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">group</i>
                                    Classificação Cliente
                                </h6>
                            </div>
                            <div class="card-content">
                                <div class="row">
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="perc_desc_a">A %</label>
                                            <input type="text" id="perc_desc_a" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="val_desc_a">R$ A</label>
                                            <input type="text" id="val_desc_a" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="perc_desc_b">B %</label>
                                            <input type="text" id="perc_desc_b" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="val_desc_b">R$ B</label>
                                            <input type="text" id="val_desc_b" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="perc_desc_c">C %</label>
                                            <input type="text" id="perc_desc_c" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="val_desc_c">R$ C</label>
                                            <input type="text" id="val_desc_c" class="form-input" value="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="perc_desc_d">D %</label>
                                            <input type="text" id="perc_desc_d" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="val_desc_d">R$ D</label>
                                            <input type="text" id="val_desc_d" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="perc_desc_e">E %</label>
                                            <input type="text" id="perc_desc_e" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label" for="val_desc_e">R$ E</label>
                                            <input type="text" id="val_desc_e" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-4">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="button" class="btn btn-secondary" id="reclassificar_percentual">
                                                <i class="material-icons">calculate</i>
                                                Reclassificar %
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GRUPO 2: TRIBUTAÇÃO AVANÇADA -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">receipt_long</i>
                                    Tributação Avançada
                                </h6>
                            </div>
                            <div class="card-content">
                                <div class="row">
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="aliquota_calculo_credito">% Cálculo do Crédito</label>
                                            <input type="text" id="aliquota_calculo_credito" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="perc_dif">% Diferimento</label>
                                            <input type="text" id="perc_dif" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="perc_redu_icms">% Redu.BC.ICMS</label>
                                            <input type="text" id="perc_redu_icms" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="aliq_fcp">%FCP</label>
                                            <input type="text" id="aliq_fcp" class="form-input" value="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-6">
                                        <div class="form-group">
                                            <label class="form-label" for="mod_deter_bc_icms">Modalidade BC ICMS</label>
                                            <select id="mod_deter_bc_icms" class="form-select">
                                                <option value="" disabled selected>Selecione</option>
                                                <option value="Margem valor agregado" selected>Margem valor agregado</option>
                                                <option value="Pauta">Pauta</option>
                                                <option value="Preco Tabela Max.">Preço Tabela Max.</option>
                                                <option value="Valor operacao">Valor operação</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col col-6">
                                        <div class="form-group">
                                            <label class="form-label" for="mod_deter_bc_icms_st">Modalidade BC ICMS ST</label>
                                            <select id="mod_deter_bc_icms_st" class="form-select">
                                                <option value="" disabled selected>Selecione</option>
                                                <option value="Preco tabelado ou max sugerido">Preço tabelado ou max sugerido</option>
                                                <option value="Lista negativa">Lista negativa</option>
                                                <option value="Lista positiva">Lista positiva</option>
                                                <option value="Lista neutra">Lista neutra</option>
                                                <option value="Margem valor agregado" selected>Margem valor agregado</option>
                                                <option value="Pauta">Pauta</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <div class="form-group">
                                            <label class="form-label" for="aliq_fcp_st">%FCP ST</label>
                                            <input type="text" id="aliq_fcp_st" class="form-input" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GRUPO 3: INFORMAÇÕES GERAIS -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">info_outline</i>
                                    Informações Gerais
                                </h6>
                            </div>
                            <div class="card-content">
                                <div class="row">
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="tamanho">Tamanho</label>
                                            <input type="text" id="tamanho" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="vencimento">Vencimento</label>
                                            <input type="date" id="vencimento" class="form-input">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="valorGelado">Gelado</label>
                                            <input type="text" id="valorGelado" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-checkbox">
                                            <input type="checkbox" id="descricao_personalizada">
                                            <label for="descricao_personalizada">Descrição Personalizada</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-12">
                                        <div class="form-group">
                                            <label class="form-label" for="prod_desc_etiqueta">Descrição Etiqueta</label>
                                            <input type="text" id="prod_desc_etiqueta" class="form-input" value="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col col-4">
                                        <div class="form-group">
                                            <label class="form-label" for="novoCodigo">Novo Código</label>
                                            <input type="text" id="novoCodigo" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-2">
                                        <div class="form-group">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="button" class="btn btn-secondary btn-icon" id="alterarCodigo" title="Alterar Código">
                                                <i class="material-icons">sync_alt</i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col col-6">
                                        <div class="form-checkbox">
                                            <input type="checkbox" id="inativo">
                                            <label for="inativo">Produto Inativo</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GRUPO 4: ESTOQUE -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">inventory</i>
                                    Estoque
                                </h6>
                            </div>
                            <div class="card-content">
                                <div class="row">
                                    <div class="col col-6">
                                        <div class="form-group">
                                            <label class="form-label" for="qtde">QTDE</label>
                                            <input type="text" id="qtde" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-6">
                                        <div class="form-group">
                                            <label class="form-label" for="qtde_min">QTDE Mínima</label>
                                            <input type="text" id="qtde_min" class="form-input" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GRUPO 5: DIMENSÕES E PESO -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">straighten</i>
                                    Dimensões e Peso
                                </h6>
                            </div>
                            <div class="card-content">
                                <div class="row">
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="comprimento">Comprimento</label>
                                            <input type="text" id="comprimento" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="largura">Largura</label>
                                            <input type="text" id="largura" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="altura">Altura</label>
                                            <input type="text" id="altura" class="form-input" value="0">
                                        </div>
                                    </div>
                                    <div class="col col-3">
                                        <div class="form-group">
                                            <label class="form-label" for="peso">Peso</label>
                                            <input type="text" id="peso" class="form-input" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- GRUPO 6: ICMS POR ESTADO -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">
                                    <i class="material-icons" style="vertical-align: middle; margin-right: 8px;">map</i>
                                    ICMS por Estado
                                </h6>
                            </div>
                            <div class="card-content">
                                <!-- Tabs de Estados -->
                                <div class="modern-tabs">
                                    <ul class="tab-list">
                                        <li class="tab-item"><a href="#PR" class="tab-link active">PR</a></li>
                                        <li class="tab-item"><a href="#AC" class="tab-link">AC</a></li>
                                        <li class="tab-item"><a href="#AL" class="tab-link">AL</a></li>
                                        <li class="tab-item"><a href="#AP" class="tab-link">AP</a></li>
                                        <li class="tab-item"><a href="#AM" class="tab-link">AM</a></li>
                                        <li class="tab-item"><a href="#BA" class="tab-link">BA</a></li>
                                        <li class="tab-item"><a href="#CE" class="tab-link">CE</a></li>
                                        <li class="tab-item"><a href="#DF" class="tab-link">DF</a></li>
                                        <li class="tab-item"><a href="#ES" class="tab-link">ES</a></li>
                                        <li class="tab-item"><a href="#GO" class="tab-link">GO</a></li>
                                        <li class="tab-item"><a href="#MA" class="tab-link">MA</a></li>
                                        <li class="tab-item"><a href="#MT" class="tab-link">MT</a></li>
                                        <li class="tab-item"><a href="#MS" class="tab-link">MS</a></li>
                                        <li class="tab-item"><a href="#MG" class="tab-link">MG</a></li>
                                        <li class="tab-item"><a href="#PA" class="tab-link">PA</a></li>
                                        <li class="tab-item"><a href="#PB" class="tab-link">PB</a></li>
                                        <li class="tab-item"><a href="#PE" class="tab-link">PE</a></li>
                                        <li class="tab-item"><a href="#PI" class="tab-link">PI</a></li>
                                        <li class="tab-item"><a href="#RR" class="tab-link">RR</a></li>
                                        <li class="tab-item"><a href="#RO" class="tab-link">RO</a></li>
                                        <li class="tab-item"><a href="#RJ" class="tab-link">RJ</a></li>
                                        <li class="tab-item"><a href="#RN" class="tab-link">RN</a></li>
                                        <li class="tab-item"><a href="#RS" class="tab-link">RS</a></li>
                                        <li class="tab-item"><a href="#SC" class="tab-link">SC</a></li>
                                        <li class="tab-item"><a href="#SP" class="tab-link">SP</a></li>
                                        <li class="tab-item"><a href="#SE" class="tab-link">SE</a></li>
                                        <li class="tab-item"><a href="#TO" class="tab-link">TO</a></li>
                                    </ul>
                                </div>

                                <!-- Conteúdo das Tabs - PR (Exemplo) -->
                                <div id="PR" class="tab-content active">
                                    <div class="row">
                                        <div class="col col-3">
                                            <div class="form-group">
                                                <label class="form-label" for="perc_redu_icms_st">% Red BC ICMS ST</label>
                                                <input type="text" id="perc_redu_icms_st" class="form-input" value="0">
                                            </div>
                                        </div>
                                        <div class="col col-3">
                                            <div class="form-group">
                                                <label class="form-label" for="perc_mv_adic_icms_st">% Marg Adic ICMS ST</label>
                                                <input type="text" id="perc_mv_adic_icms_st" class="form-input" value="0">
                                            </div>
                                        </div>
                                        <div class="col col-3">
                                            <div class="form-group">
                                                <label class="form-label" for="aliq_icms_st">% ICMS ST</label>
                                                <input type="text" id="aliq_icms_st" class="form-input" value="0">
                                            </div>
                                        </div>
                                        <div class="col col-3">
                                            <div class="form-group">
                                                <label class="form-label" for="aliq_icms">% ICMS</label>
                                                <input type="text" id="aliq_icms" class="form-input" value="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Outros estados teriam estrutura similar -->
                                <div id="AC" class="tab-content">
                                    <p style="text-align: center; color: #999; padding: 20px;">
                                        Configurações de ICMS para o estado do Acre
                                    </p>
                                </div>

                                <!-- Placeholder para outros estados -->
                                <div id="AL" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Alagoas</p></div>
                                <div id="AP" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Amapá</p></div>
                                <div id="AM" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Amazonas</p></div>
                                <div id="BA" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Bahia</p></div>
                                <div id="CE" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Ceará</p></div>
                                <div id="DF" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Distrito Federal</p></div>
                                <div id="ES" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Espírito Santo</p></div>
                                <div id="GO" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Goiás</p></div>
                                <div id="MA" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Maranhão</p></div>
                                <div id="MT" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Mato Grosso</p></div>
                                <div id="MS" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Mato Grosso do Sul</p></div>
                                <div id="MG" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Minas Gerais</p></div>
                                <div id="PA" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Pará</p></div>
                                <div id="PB" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Paraíba</p></div>
                                <div id="PE" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Pernambuco</p></div>
                                <div id="PI" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Piauí</p></div>
                                <div id="RR" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Roraima</p></div>
                                <div id="RO" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Rondônia</p></div>
                                <div id="RJ" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Rio de Janeiro</p></div>
                                <div id="RN" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Rio Grande do Norte</p></div>
                                <div id="RS" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Rio Grande do Sul</p></div>
                                <div id="SC" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Santa Catarina</p></div>
                                <div id="SP" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para São Paulo</p></div>
                                <div id="SE" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Sergipe</p></div>
                                <div id="TO" class="tab-content"><p style="text-align: center; color: #999; padding: 20px;">Configurações de ICMS para Tocantins</p></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEÇÃO 4: IPI/PIS/COFINS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">receipt</i>
                        <span class="title">IPI/PIS/COFINS</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <h6 class="mb-2">IPI</h6>
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="aliquota_ipi">Alíquota IPI (%)</label>
                                    <input type="number" id="aliquota_ipi" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="cst_ipi">CST IPI</label>
                                    <select id="cst_ipi" class="form-select">
                                        <option value="0">00 - Entrada com recuperação de crédito</option>
                                        <option value="1">01 - Entrada tributada com alíquota zero</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-2 mt-3">PIS</h6>
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="aliquota_pis">Alíquota PIS (%)</label>
                                    <input type="number" id="aliquota_pis" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="cst_pis">CST PIS</label>
                                    <select id="cst_pis" class="form-select">
                                        <option value="99">99 - Outras Operações</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-2 mt-3">COFINS</h6>
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="aliquota_cofins">Alíquota COFINS (%)</label>
                                    <input type="number" id="aliquota_cofins" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="cst_cofins">CST COFINS</label>
                                    <select id="cst_cofins" class="form-select">
                                        <option value="99">99 - Outras Operações</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEÇÃO 5: GRADE -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">apps</i>
                        <span class="title">Grade</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_codigo_gtin">Código</label>
                                    <input type="text" id="prod_gd_codigo_gtin" class="form-input">
                                </div>
                            </div>
                            <div class="col col-9">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_nome">Nome</label>
                                    <input type="text" id="prod_gd_nome" class="form-input">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_variacao">Variação</label>
                                    <input type="text" id="prod_gd_variacao" class="form-input">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_caracteristica">Característica</label>
                                    <input type="text" id="prod_gd_caracteristica" class="form-input">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-success" onclick="adicionarItemGrade()">
                                        <i class="material-icons">add</i>
                                        Adicionar
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Descrição</th>
                                    <th>Variação</th>
                                    <th>Característica</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="grade-table">
                                <!-- Itens serão adicionados dinamicamente -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- SEÇÃO 6: IMAGENS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">image</i>
                        <span class="title">Imagens</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <div class="row">
                            <div class="col col-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title">Preview da Imagem</h6>
                                    </div>
                                    <div class="card-content">
                                        <div id="image-preview-container" style="min-height: 300px; display: flex; align-items: center; justify-content: center; border: 2px dashed #ddd; border-radius: 8px;">
                                            <div style="text-align: center; color: #999;">
                                                <i class="material-icons" style="font-size: 48px;">image</i>
                                                <p>Selecione uma posição para visualizar</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title">Gerenciar Imagens</h6>
                                    </div>
                                    <div class="card-content">
                                        <div id="image-positions-list">
                                            <!-- Lista de posições será preenchida dinamicamente -->
                                        </div>
                                        <div class="text-center mt-3">
                                            <input type="file" id="image-upload-input" accept="image/*" style="display: none;">
                                            <button type="button" class="btn btn-primary" onclick="document.getElementById('image-upload-input').click()">
                                                <i class="material-icons">cloud_upload</i>
                                                Enviar Imagem
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Botões de Ação -->
        <div class="card mt-3">
            <div class="card-content">
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="retornarPrincipal()">
                        <i class="material-icons">arrow_back</i>
                        Voltar
                    </button>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-danger" onclick="limparProdutos()">
                            <i class="material-icons">clear</i>
                            Limpar
                        </button>
                        <button type="button" class="btn btn-success" onclick="gravarProdutos()">
                            <i class="material-icons">save</i>
                            Gravar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modais -->
    <div id="modalGrupo" class="modal">
        <div class="modal-content">
            <h5>Adicionar Grupo</h5>
            <div class="form-group">
                <label class="form-label" for="grupo_novo">Nome do Grupo</label>
                <input type="text" id="grupo_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalGrupo')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarGrupo()">Adicionar</button>
        </div>
    </div>

    <div id="modalSubGrupo" class="modal">
        <div class="modal-content">
            <h5>Adicionar Sub Grupo</h5>
            <div class="form-group">
                <label class="form-label" for="subgrupo_novo">Nome do Sub Grupo</label>
                <input type="text" id="subgrupo_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalSubGrupo')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarSubGrupo()">Adicionar</button>
        </div>
    </div>

    <div id="modalCategoria" class="modal">
        <div class="modal-content">
            <h5>Adicionar Categoria</h5>
            <div class="form-group">
                <label class="form-label" for="categoria_novo">Nome da Categoria</label>
                <input type="text" id="categoria_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalCategoria')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarCategoria()">Adicionar</button>
        </div>
    </div>

    <div id="modalUnidade" class="modal">
        <div class="modal-content">
            <h5>Adicionar Unidade</h5>
            <div class="form-group">
                <label class="form-label" for="unidade_novo">Nome da Unidade</label>
                <input type="text" id="unidade_novo" class="form-input" placeholder="Ex: KG, UN, LT">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalUnidade')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarUnidade()">Adicionar</button>
        </div>
    </div>

    <div id="modalFornecedor" class="modal">
        <div class="modal-content">
            <h5>Adicionar Fornecedor</h5>
            <div class="form-group">
                <label class="form-label" for="fornecedor_novo">Razão Social do Fornecedor</label>
                <input type="text" id="fornecedor_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalFornecedor')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarFornecedor()">Adicionar</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/produtos-modern.js"></script>
    <script>
        // Funções específicas do produto (mantém compatibilidade)
        function retornarPrincipal() {
            showToast('Voltando para a tela principal...', 2000, 'info');
            // Implementar navegação
        }

        function limparProdutos() {
            if (confirm('Deseja realmente limpar todos os campos?')) {
                document.getElementById('produto-form').reset();
                showToast('Formulário limpo com sucesso!', 3000, 'success');
            }
        }

        function gravarProdutos() {
            showToast('Salvando produto...', 2000, 'info');
            // Implementar salvamento
        }

        function adicionarGrupo() {
            const nome = document.getElementById('grupo_novo').value;
            if (nome) {
                // Adicionar ao select
                const select = document.getElementById('grupo');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Grupo adicionado com sucesso!', 3000, 'success');
                closeModal('modalGrupo');
                document.getElementById('grupo_novo').value = '';
            }
        }

        function adicionarSubGrupo() {
            const nome = document.getElementById('subgrupo_novo').value;
            if (nome) {
                const select = document.getElementById('subgrupo');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Sub Grupo adicionado com sucesso!', 3000, 'success');
                closeModal('modalSubGrupo');
                document.getElementById('subgrupo_novo').value = '';
            }
        }

        function adicionarCategoria() {
            const nome = document.getElementById('categoria_novo').value;
            if (nome) {
                const select = document.getElementById('categoria');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Categoria adicionada com sucesso!', 3000, 'success');
                closeModal('modalCategoria');
                document.getElementById('categoria_novo').value = '';
            }
        }

        function adicionarUnidade() {
            const nome = document.getElementById('unidade_novo').value;
            if (nome) {
                const select = document.getElementById('unidade');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Unidade adicionada com sucesso!', 3000, 'success');
                closeModal('modalUnidade');
                document.getElementById('unidade_novo').value = '';
            }
        }

        function adicionarFornecedor() {
            const nome = document.getElementById('fornecedor_novo').value;
            if (nome) {
                const select = document.getElementById('fornecedor');
                const option = document.createElement('option');
                option.value = nome;
                option.text = nome;
                option.selected = true;
                select.appendChild(option);

                showToast('Fornecedor adicionado com sucesso!', 3000, 'success');
                closeModal('modalFornecedor');
                document.getElementById('fornecedor_novo').value = '';
            }
        }

        function verificarCodigo() {
            const codigo = document.getElementById('codigo_gtin').value;
            if (codigo) {
                showToast('Verificando código...', 2000, 'info');
                // Aqui seria implementada a verificação real
            }
        }

        function selecionouFornecedor() {
            const fornecedor = document.getElementById('fornecedor').value;
            console.log('Fornecedor selecionado:', fornecedor);
        }

        // ===== FUNÇÕES DA COMPOSIÇÃO =====
        function adicionarItemComposicao() {
            const codigo = document.getElementById('codigo_gtin_composicao').value.trim();
            const descricao = document.getElementById('descricao_composicao').value.trim();
            const qtde = document.getElementById('qtde_composicao').value.trim();

            // Validações
            if (!codigo) {
                showToast('Digite o código do produto', 3000, 'warning');
                document.getElementById('codigo_gtin_composicao').focus();
                return;
            }

            if (!qtde || parseFloat(qtde) <= 0) {
                showToast('Digite uma quantidade válida', 3000, 'warning');
                document.getElementById('qtde_composicao').focus();
                return;
            }

            // Verificar se o código já existe na composição
            const tbody = document.getElementById('composicao-table-body');
            const existingRows = tbody.querySelectorAll('tr:not(.exemplo-item)');

            for (let row of existingRows) {
                const existingCodigo = row.cells[0].textContent;
                if (existingCodigo === codigo) {
                    showToast('Este produto já está na composição', 3000, 'warning');
                    return;
                }
            }

            // Remover linha de exemplo se existir
            const exemploItem = tbody.querySelector('.exemplo-item');
            if (exemploItem) {
                exemploItem.remove();
            }

            // Criar nova linha
            const newRow = tbody.insertRow();
            newRow.innerHTML = `
                <td>${codigo}</td>
                <td>${descricao || 'Produto não encontrado'}</td>
                <td>${parseFloat(qtde).toFixed(2)}</td>
                <td>
                    <button type="button" class="btn btn-danger btn-small" onclick="removerItemComposicao(this)" title="Remover">
                        <i class="material-icons">delete</i>
                    </button>
                </td>
            `;

            // Limpar campos
            document.getElementById('codigo_gtin_composicao').value = '';
            document.getElementById('descricao_composicao').value = '';
            document.getElementById('qtde_composicao').value = '';

            showToast('Item adicionado à composição!', 3000, 'success');

            // Focar no campo código para próximo item
            document.getElementById('codigo_gtin_composicao').focus();
        }

        function removerItemComposicao(button) {
            if (confirm('Deseja remover este item da composição?')) {
                const row = button.closest('tr');
                const codigo = row.cells[0].textContent;
                row.remove();

                // Se não há mais itens, mostrar mensagem
                const tbody = document.getElementById('composicao-table-body');
                if (tbody.children.length === 0) {
                    const newRow = tbody.insertRow();
                    newRow.className = 'exemplo-item';
                    newRow.innerHTML = `
                        <td colspan="4" style="text-align: center; color: #999; font-style: italic;">
                            Nenhum item adicionado à composição
                        </td>
                    `;
                }

                showToast(`Item ${codigo} removido da composição`, 3000, 'success');
            }
        }

        // Simular busca de produto por código (na implementação real, seria AJAX)
        document.getElementById('codigo_gtin_composicao').addEventListener('blur', function() {
            const codigo = this.value.trim();
            const descricaoField = document.getElementById('descricao_composicao');

            if (codigo) {
                // Simular busca (na implementação real seria AJAX)
                setTimeout(() => {
                    // Exemplo de produtos fictícios
                    const produtos = {
                        '123': 'Produto Exemplo A',
                        '456': 'Produto Exemplo B',
                        '789': 'Produto Exemplo C'
                    };

                    descricaoField.value = produtos[codigo] || 'Produto não encontrado';
                }, 300);
            } else {
                descricaoField.value = '';
            }
        });

        // ===== FUNÇÕES DA SEÇÃO OUTROS =====

        // Inicializar tabs
        document.addEventListener('DOMContentLoaded', function() {
            initTabs();
        });

        function initTabs() {
            const tabLinks = document.querySelectorAll('.tab-link');
            const tabContents = document.querySelectorAll('.tab-content');

            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remover active de todos os links e conteúdos
                    tabLinks.forEach(l => l.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // Adicionar active ao link clicado
                    this.classList.add('active');

                    // Mostrar conteúdo correspondente
                    const targetId = this.getAttribute('href').substring(1);
                    const targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        }

        // Reclassificar percentuais
        document.getElementById('reclassificar_percentual').addEventListener('click', function(e) {
            e.preventDefault();
            showToast('Reclassificando percentuais...', 3000, 'info');
            // Implementar lógica de reclassificação
        });

        // Alterar código
        document.getElementById('alterarCodigo').addEventListener('click', function(e) {
            e.preventDefault();
            const novoCodigo = document.getElementById('novoCodigo').value;

            if (!novoCodigo || novoCodigo === '0') {
                showToast('Digite um novo código válido', 3000, 'warning');
                document.getElementById('novoCodigo').focus();
                return;
            }

            if (confirm(`Deseja alterar o código para: ${novoCodigo}?`)) {
                document.getElementById('codigo_gtin').value = novoCodigo;
                document.getElementById('novoCodigo').value = '0';
                showToast('Código alterado com sucesso!', 3000, 'success');
            }
        });

        function adicionarItemGrade() {
            showToast('Item adicionado à grade!', 3000, 'success');
        }

        // Demonstração de toast
        setTimeout(() => {
            showToast('Sistema moderno carregado com sucesso! 🎉', 4000, 'success');
        }, 1000);
    </script>
</body>
</html>
