<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos - Edição Moderna</title>
    
    <!-- CSS Moderno -->
    <link rel="stylesheet" href="css/produtos-modern.css">
    
    <!-- Material Icons (mantemos apenas os ícones) -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Cabeçalho -->
        <div class="card mb-3">
            <div class="card-content">
                <h1 class="card-title d-flex align-items-center gap-2">
                    <i class="material-icons">inventory_2</i>
                    Edição de Produto
                </h1>
            </div>
        </div>

        <!-- Formulário Principal -->
        <form id="produto-form">
            <div class="modern-accordion">
                
                <!-- SEÇÃO 1: INFORMAÇÕES BÁSICAS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">info</i>
                        <span class="title">Informações Básicas</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <!-- Grupo 1: Identificação -->
                        <div class="row">
                            <div class="col col-4">
                                <div class="form-group">
                                    <label class="form-label" for="codigo_gtin">Código GTIN</label>
                                    <input type="text" id="codigo_gtin" class="form-input" placeholder="Digite o código GTIN" onfocusout="verificarCodigo();" required>
                                    <input type="hidden" id="codigo_interno" value="0">
                                </div>
                            </div>
                            <div class="col col-8">
                                <div class="form-group">
                                    <label class="form-label" for="descricao">Descrição</label>
                                    <input type="text" id="descricao" class="form-input" placeholder="Descrição do produto" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col col-12">
                                <div class="form-group">
                                    <label class="form-label" for="descricao_detalhada">Descrição Detalhada</label>
                                    <input type="text" id="descricao_detalhada" class="form-input" placeholder="Descrição detalhada do produto">
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 2: Classificação -->
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="grupo">Grupo</label>
                                    <select id="grupo" class="form-select">
                                        <option value="">Selecione um grupo</option>
                                        <option value="PRINCIPAL">PRINCIPAL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalGrupo')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="subgrupo">Sub Grupo</label>
                                    <select id="subgrupo" class="form-select">
                                        <option value="">Selecione um subgrupo</option>
                                        <option value="PRINCIPAL">PRINCIPAL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalSubGrupo')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="categoria">Categoria</label>
                                    <select id="categoria" class="form-select">
                                        <option value="">Selecione uma categoria</option>
                                        <option value="PRINCIPAL">PRINCIPAL</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalCategoria')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 3: Unidade e Preços -->
                        <div class="row">
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="unidade">Unidade</label>
                                    <select id="unidade" class="form-select">
                                        <option value="">Selecione</option>
                                        <option value="UN">UN</option>
                                        <option value="KG">KG</option>
                                        <option value="LT">LT</option>
                                        <option value="MT">MT</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalUnidade')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="preco_venda">$ Venda</label>
                                    <input type="text" id="preco_venda" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="preco_compra">$ Compra</label>
                                    <input type="text" id="preco_compra" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="perc_lucro">% Lucro</label>
                                    <input type="text" id="perc_lucro" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-secondary btn-icon" title="Calcular">
                                        <i class="material-icons">calculate</i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 4: Tributação -->
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="ncm">NCM</label>
                                    <input type="text" id="ncm" class="form-input" placeholder="NCM">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="cest">CEST</label>
                                    <input type="text" id="cest" class="form-input" placeholder="CEST">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="cfop">CFOP</label>
                                    <input type="text" id="cfop" class="form-input" value="5102">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="situacao_tributaria">Situação Tributária</label>
                                    <select id="situacao_tributaria" class="form-select">
                                        <option value="" disabled selected>Selecione</option>
                                        <option value="101">101 - Tributação com permissão de crédito</option>
                                        <option value="102" selected>102 - Tributação sem permissão de crédito</option>
                                        <option value="103">103 - Isenção do ICMS para faixa de receita bruta</option>
                                        <option value="201">201 - Tributada COM permissão de crédito e com cobrança do ICMS POR ST</option>
                                        <option value="202">202 - Tributada SEM permissão de crédito e com cobrança do ICMS POR ST</option>
                                        <option value="203">203 - Isenção do ICMS para faixa de receita bruta e com cobrança do ICMS POR ST</option>
                                        <option value="300">300 - Imune</option>
                                        <option value="500">500 - ICMS cobrado anteriormente por ST ou por antecipação</option>
                                        <option value="900">900 - Outros</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 5: ICMS e Configurações -->
                        <div class="row">
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="perc_icms">% ICMS</label>
                                    <input type="text" id="perc_icms" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-checkbox">
                                    <input type="checkbox" id="produto_balanca">
                                    <label for="produto_balanca">Prod.Balança</label>
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="vadidade">Validade</label>
                                    <input type="text" id="vadidade" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="data_cadastro">Dt Cadastro</label>
                                    <input type="text" id="data_cadastro" class="form-input" disabled>
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="data_alteracao">Dt Alteração</label>
                                    <input type="text" id="data_alteracao" class="form-input" disabled>
                                </div>
                            </div>
                        </div>

                        <!-- Grupo 6: Fornecedor -->
                        <div class="row">
                            <div class="col col-7">
                                <div class="form-group">
                                    <label class="form-label" for="fornecedor">Fornecedor</label>
                                    <select id="fornecedor" class="form-select" onchange="selecionouFornecedor()">
                                        <option value="">Selecione um fornecedor</option>
                                        <option value="0">Nenhum</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col col-1">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-primary btn-icon" onclick="openModal('modalFornecedor')">
                                        <i class="material-icons">add</i>
                                    </button>
                                </div>
                            </div>
                            <div class="col col-4">
                                <div class="form-checkbox">
                                    <input type="checkbox" id="vender_ecomerce">
                                    <label for="vender_ecomerce">Vender no E-commerce</label>
                                </div>
                                <div class="form-checkbox">
                                    <input type="checkbox" id="produto_producao">
                                    <label for="produto_producao">Produto de Produção</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEÇÃO 2: COMPOSIÇÃO -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">hive</i>
                        <span class="title">Composição</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <div class="row">
                            <div class="col col-4">
                                <div class="form-group">
                                    <label class="form-label" for="codigo_composicao">Código</label>
                                    <input type="text" id="codigo_composicao" class="form-input">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="descricao_composicao">Descrição</label>
                                    <input type="text" id="descricao_composicao" class="form-input">
                                </div>
                            </div>
                            <div class="col col-2">
                                <div class="form-group">
                                    <label class="form-label" for="qtde_composicao">Quantidade</label>
                                    <input type="number" id="qtde_composicao" class="form-input" step="0.01">
                                </div>
                            </div>
                        </div>
                        
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Descrição</th>
                                    <th>Quantidade</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="composicao-table">
                                <!-- Itens serão adicionados dinamicamente -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- SEÇÃO 3: OUTROS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">tune</i>
                        <span class="title">Outros</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <h6 class="mb-2">Dimensões e Peso</h6>
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="comprimento">Comprimento (cm)</label>
                                    <input type="number" id="comprimento" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="largura">Largura (cm)</label>
                                    <input type="number" id="largura" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="altura">Altura (cm)</label>
                                    <input type="number" id="altura" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="peso">Peso (kg)</label>
                                    <input type="number" id="peso" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-2 mt-3">Estoque</h6>
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="qtde">Quantidade</label>
                                    <input type="number" id="qtde" class="form-input" value="0">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="qtde_min">Quantidade Mínima</label>
                                    <input type="number" id="qtde_min" class="form-input" value="0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEÇÃO 4: IPI/PIS/COFINS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">receipt</i>
                        <span class="title">IPI/PIS/COFINS</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <h6 class="mb-2">IPI</h6>
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="aliquota_ipi">Alíquota IPI (%)</label>
                                    <input type="number" id="aliquota_ipi" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="cst_ipi">CST IPI</label>
                                    <select id="cst_ipi" class="form-select">
                                        <option value="0">00 - Entrada com recuperação de crédito</option>
                                        <option value="1">01 - Entrada tributada com alíquota zero</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-2 mt-3">PIS</h6>
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="aliquota_pis">Alíquota PIS (%)</label>
                                    <input type="number" id="aliquota_pis" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="cst_pis">CST PIS</label>
                                    <select id="cst_pis" class="form-select">
                                        <option value="99">99 - Outras Operações</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <h6 class="mb-2 mt-3">COFINS</h6>
                        <div class="row">
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="aliquota_cofins">Alíquota COFINS (%)</label>
                                    <input type="number" id="aliquota_cofins" class="form-input" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label" for="cst_cofins">CST COFINS</label>
                                    <select id="cst_cofins" class="form-select">
                                        <option value="99">99 - Outras Operações</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEÇÃO 5: GRADE -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">apps</i>
                        <span class="title">Grade</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_codigo_gtin">Código</label>
                                    <input type="text" id="prod_gd_codigo_gtin" class="form-input">
                                </div>
                            </div>
                            <div class="col col-9">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_nome">Nome</label>
                                    <input type="text" id="prod_gd_nome" class="form-input">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_variacao">Variação</label>
                                    <input type="text" id="prod_gd_variacao" class="form-input">
                                </div>
                            </div>
                            <div class="col col-3">
                                <div class="form-group">
                                    <label class="form-label" for="prod_gd_caracteristica">Característica</label>
                                    <input type="text" id="prod_gd_caracteristica" class="form-input">
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-success" onclick="adicionarItemGrade()">
                                        <i class="material-icons">add</i>
                                        Adicionar
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Descrição</th>
                                    <th>Variação</th>
                                    <th>Característica</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="grade-table">
                                <!-- Itens serão adicionados dinamicamente -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- SEÇÃO 6: IMAGENS -->
                <div class="accordion-item">
                    <div class="accordion-header">
                        <i class="material-icons icon">image</i>
                        <span class="title">Imagens</span>
                        <i class="material-icons chevron">expand_more</i>
                    </div>
                    <div class="accordion-content">
                        <div class="row">
                            <div class="col col-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title">Preview da Imagem</h6>
                                    </div>
                                    <div class="card-content">
                                        <div id="image-preview-container" style="min-height: 300px; display: flex; align-items: center; justify-content: center; border: 2px dashed #ddd; border-radius: 8px;">
                                            <div style="text-align: center; color: #999;">
                                                <i class="material-icons" style="font-size: 48px;">image</i>
                                                <p>Selecione uma posição para visualizar</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col col-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title">Gerenciar Imagens</h6>
                                    </div>
                                    <div class="card-content">
                                        <div id="image-positions-list">
                                            <!-- Lista de posições será preenchida dinamicamente -->
                                        </div>
                                        <div class="text-center mt-3">
                                            <input type="file" id="image-upload-input" accept="image/*" style="display: none;">
                                            <button type="button" class="btn btn-primary" onclick="document.getElementById('image-upload-input').click()">
                                                <i class="material-icons">cloud_upload</i>
                                                Enviar Imagem
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- Botões de Ação -->
        <div class="card mt-3">
            <div class="card-content">
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="retornarPrincipal()">
                        <i class="material-icons">arrow_back</i>
                        Voltar
                    </button>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-danger" onclick="limparProdutos()">
                            <i class="material-icons">clear</i>
                            Limpar
                        </button>
                        <button type="button" class="btn btn-success" onclick="gravarProdutos()">
                            <i class="material-icons">save</i>
                            Gravar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modais -->
    <div id="modalGrupo" class="modal">
        <div class="modal-content">
            <h5>Adicionar Grupo</h5>
            <div class="form-group">
                <label class="form-label" for="grupo_novo">Nome do Grupo</label>
                <input type="text" id="grupo_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalGrupo')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarGrupo()">Adicionar</button>
        </div>
    </div>

    <div id="modalSubGrupo" class="modal">
        <div class="modal-content">
            <h5>Adicionar Sub Grupo</h5>
            <div class="form-group">
                <label class="form-label" for="subgrupo_novo">Nome do Sub Grupo</label>
                <input type="text" id="subgrupo_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalSubGrupo')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarSubGrupo()">Adicionar</button>
        </div>
    </div>

    <div id="modalCategoria" class="modal">
        <div class="modal-content">
            <h5>Adicionar Categoria</h5>
            <div class="form-group">
                <label class="form-label" for="categoria_novo">Nome da Categoria</label>
                <input type="text" id="categoria_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalCategoria')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarCategoria()">Adicionar</button>
        </div>
    </div>

    <div id="modalUnidade" class="modal">
        <div class="modal-content">
            <h5>Adicionar Unidade</h5>
            <div class="form-group">
                <label class="form-label" for="unidade_novo">Nome da Unidade</label>
                <input type="text" id="unidade_novo" class="form-input" placeholder="Ex: KG, UN, LT">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalUnidade')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarUnidade()">Adicionar</button>
        </div>
    </div>

    <div id="modalFornecedor" class="modal">
        <div class="modal-content">
            <h5>Adicionar Fornecedor</h5>
            <div class="form-group">
                <label class="form-label" for="fornecedor_novo">Razão Social do Fornecedor</label>
                <input type="text" id="fornecedor_novo" class="form-input">
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('modalFornecedor')">Cancelar</button>
            <button type="button" class="btn btn-primary" onclick="adicionarFornecedor()">Adicionar</button>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/produtos-modern.js"></script>
    <script>
        // Funções específicas do produto (mantém compatibilidade)
        function retornarPrincipal() {
            showToast('Voltando para a tela principal...', 2000, 'info');
            // Implementar navegação
        }

        function limparProdutos() {
            if (confirm('Deseja realmente limpar todos os campos?')) {
                document.getElementById('produto-form').reset();
                showToast('Formulário limpo com sucesso!', 3000, 'success');
            }
        }

        function gravarProdutos() {
            showToast('Salvando produto...', 2000, 'info');
            // Implementar salvamento
        }

        function adicionarGrupo() {
            const nome = document.getElementById('grupo_novo').value;
            if (nome) {
                // Adicionar ao select
                const select = document.getElementById('grupo');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Grupo adicionado com sucesso!', 3000, 'success');
                closeModal('modalGrupo');
                document.getElementById('grupo_novo').value = '';
            }
        }

        function adicionarSubGrupo() {
            const nome = document.getElementById('subgrupo_novo').value;
            if (nome) {
                const select = document.getElementById('subgrupo');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Sub Grupo adicionado com sucesso!', 3000, 'success');
                closeModal('modalSubGrupo');
                document.getElementById('subgrupo_novo').value = '';
            }
        }

        function adicionarCategoria() {
            const nome = document.getElementById('categoria_novo').value;
            if (nome) {
                const select = document.getElementById('categoria');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Categoria adicionada com sucesso!', 3000, 'success');
                closeModal('modalCategoria');
                document.getElementById('categoria_novo').value = '';
            }
        }

        function adicionarUnidade() {
            const nome = document.getElementById('unidade_novo').value;
            if (nome) {
                const select = document.getElementById('unidade');
                const option = document.createElement('option');
                option.value = nome.toUpperCase();
                option.text = nome.toUpperCase();
                option.selected = true;
                select.appendChild(option);

                showToast('Unidade adicionada com sucesso!', 3000, 'success');
                closeModal('modalUnidade');
                document.getElementById('unidade_novo').value = '';
            }
        }

        function adicionarFornecedor() {
            const nome = document.getElementById('fornecedor_novo').value;
            if (nome) {
                const select = document.getElementById('fornecedor');
                const option = document.createElement('option');
                option.value = nome;
                option.text = nome;
                option.selected = true;
                select.appendChild(option);

                showToast('Fornecedor adicionado com sucesso!', 3000, 'success');
                closeModal('modalFornecedor');
                document.getElementById('fornecedor_novo').value = '';
            }
        }

        function verificarCodigo() {
            const codigo = document.getElementById('codigo_gtin').value;
            if (codigo) {
                showToast('Verificando código...', 2000, 'info');
                // Aqui seria implementada a verificação real
            }
        }

        function selecionouFornecedor() {
            const fornecedor = document.getElementById('fornecedor').value;
            console.log('Fornecedor selecionado:', fornecedor);
        }

        function adicionarItemGrade() {
            showToast('Item adicionado à grade!', 3000, 'success');
        }

        // Demonstração de toast
        setTimeout(() => {
            showToast('Sistema moderno carregado com sucesso! 🎉', 4000, 'success');
        }, 1000);
    </script>
</body>
</html>
