{"name": "magnific-popup", "version": "1.1.0", "title": "Magnific Popup", "description": "Lightbox and modal dialog plugin. Can display inline HTML, iframes (YouTube video, Vimeo, Google Maps), or an image gallery. Animation effects are added with CSS3 transitions. For jQuery or Zepto.", "keywords": ["ecosystem:jquery", "jquery-plugin", "zepto", "lightbox", "popup", "modal", "window", "dialog", "gallery", "j<PERSON>y", "photo", "responsive", "mobile"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "web": "http://dimsemenov.com"}, "license": "MIT", "bugs": {"url": "https://github.com/dimsemenov/Magnific-Popup/issues"}, "main": "dist/jquery.magnific-popup.js", "style": "dist/magnific-popup.css", "homepage": "http://dimsemenov.com/plugins/magnific-popup/", "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt jshint"}, "devDependencies": {"grunt": "~0.4.5", "grunt-contrib-clean": "~0.6.0", "grunt-contrib-concat": "^0.5.1", "grunt-contrib-copy": "~0.8.1", "grunt-contrib-cssmin": "^0.14.0", "grunt-contrib-jshint": "~0.11.3", "grunt-contrib-uglify": "~0.9.2", "grunt-contrib-watch": "~0.6.1", "grunt-jekyll": "~0.4.2", "grunt-sass": "~1.0.0"}, "repository": {"type": "git", "url": "https://github.com/dimsemenov/Magnific-Popup.git"}}