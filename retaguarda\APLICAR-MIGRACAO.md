# 🚀 Como Aplicar a Migração no produtos.php

## ⚠️ IMPORTANTE: Fazer Backup Primeiro!

```bash
# Fazer backup do arquivo original
cp produtos.php produtos.php.backup
cp css/materialize.min.css css/materialize.min.css.backup
cp js/materialize.min.js js/materialize.min.js.backup
```

## 📋 Passo a Passo

### **1. Atualizar as Inclusões de CSS e JS**

No arquivo `produtos.php`, localize a seção `<head>` e faça as seguintes alterações:

#### ❌ **Remover/Comentar:**
```html
<!-- <link rel="stylesheet" href="css/materialize.min.css"> -->
```

#### ✅ **Adicionar:**
```html
<!-- CSS Moderno -->
<link rel="stylesheet" href="css/produtos-modern.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Manter Material Icons -->
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
```

#### ❌ **Remover/Comentar no final do body:**
```html
<!-- <script src="js/materialize.min.js"></script> -->
```

#### ✅ **Adicionar antes do `</body>`:**
```html
<!-- JavaScript Moderno -->
<script src="js/produtos-modern.js"></script>

<!-- Script de conversão automática (temporário) -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Aguardar um pouco para garantir que tudo carregou
    setTimeout(() => {
        if (typeof convertMaterializeToModern === 'function') {
            convertMaterializeToModern();
            console.log('🎉 Conversão automática aplicada!');
        }
    }, 500);
});
</script>
```

### **2. Conversão Automática vs Manual**

Você tem duas opções:

#### **Opção A: Conversão Automática (Recomendada)**
- Adicione o script acima
- Abra a página no navegador
- A conversão será feita automaticamente
- Depois remova o script temporário

#### **Opção B: Conversão Manual**
- Siga as instruções detalhadas abaixo

---

## 🔧 Conversão Manual (se preferir)

### **2.1. Converter Estrutura Principal**

#### ❌ **Localizar e substituir:**
```html
<ul class="collapsible">
```

#### ✅ **Por:**
```html
<div class="modern-accordion">
```

#### ❌ **Localizar e substituir:**
```html
</ul>
```

#### ✅ **Por:**
```html
</div>
```

### **2.2. Converter Cada Seção**

#### ❌ **Estrutura antiga:**
```html
<li>
    <div class="collapsible-header #7986cb indigo lighten-2 white-text">
        <i class="material-icons">Abc</i>Informações Básicas
    </div>
    <div class="collapsible-body">
        <!-- conteúdo -->
    </div>
</li>
```

#### ✅ **Estrutura nova:**
```html
<div class="accordion-item">
    <div class="accordion-header">
        <i class="material-icons icon">info</i>
        <span class="title">Informações Básicas</span>
        <i class="material-icons chevron">expand_more</i>
    </div>
    <div class="accordion-content">
        <!-- conteúdo -->
    </div>
</div>
```

### **2.3. Atualizar Ícones das Seções**

| Seção | Ícone Antigo | Ícone Novo |
|-------|-------------|------------|
| Informações Básicas | `Abc` | `info` |
| Composição | `hive` | `hive` |
| Outros | `square_foot` | `tune` |
| IPI/PIS/COFINS | (varia) | `receipt` |
| Grade | `apps` | `apps` |
| Imagens | `image` | `image` |

### **2.4. Converter Grid System**

#### ❌ **Substituir:**
```html
<div class="col s12 l4">
<div class="col s6 l3">
<div class="col s4 l2">
```

#### ✅ **Por:**
```html
<div class="col col-4">
<div class="col col-3">
<div class="col col-2">
```

### **2.5. Converter Campos de Formulário**

#### ❌ **Estrutura antiga:**
```html
<div class="input-field col s12 l4">
    <input type="text" id="codigo_gtin" class="input-field" placeholder="" />
    <label class="active" for="codigo_gtin">Código</label>
</div>
```

#### ✅ **Estrutura nova:**
```html
<div class="col col-4">
    <div class="form-group">
        <label class="form-label" for="codigo_gtin">Código GTIN</label>
        <input type="text" id="codigo_gtin" class="form-input" placeholder="Digite o código GTIN" />
    </div>
</div>
```

### **2.6. Converter Selects**

#### ❌ **Estrutura antiga:**
```html
<div class="col s10 l3">
    <label class="active">Grupo</label>
    <select id="grupo" class="browser-default">
        <!-- opções -->
    </select>
</div>
```

#### ✅ **Estrutura nova:**
```html
<div class="col col-3">
    <div class="form-group">
        <label class="form-label" for="grupo">Grupo</label>
        <select id="grupo" class="form-select">
            <!-- opções -->
        </select>
    </div>
</div>
```

### **2.7. Converter Checkboxes**

#### ❌ **Estrutura antiga:**
```html
<div class="col s12 l4">
    <input type="checkbox" valign="botton" id="vender_ecomerce" />
    <label class="active" for="vender_ecomerce">Vender no E-comerce</label>
</div>
```

#### ✅ **Estrutura nova:**
```html
<div class="col col-4">
    <div class="form-checkbox">
        <input type="checkbox" id="vender_ecomerce" />
        <label for="vender_ecomerce">Vender no E-commerce</label>
    </div>
</div>
```

### **2.8. Converter Botões**

#### ❌ **Estrutura antiga:**
```html
<a href="javascript:gravarProdutos()" class="waves-effect waves-light btn green" id="gravarProdutos">
    <i class="material-icons">save</i>
    <span class="hide-on-small-only">Gravar</span>
</a>
```

#### ✅ **Estrutura nova:**
```html
<button type="button" class="btn btn-success" onclick="gravarProdutos()" id="gravarProdutos">
    <i class="material-icons">save</i>
    Gravar
</button>
```

### **2.9. Converter Tabelas**

#### ❌ **Classe antiga:**
```html
<table class="responsive-table striped" id='userTableGrade'>
```

#### ✅ **Classe nova:**
```html
<table class="modern-table" id='userTableGrade'>
```

### **2.10. Atualizar Botões de Ação**

Localizar a seção `.form-buttons` e substituir por:

```html
<div class="card mt-3">
    <div class="card-content">
        <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-secondary" onclick="retornarPrincipal()">
                <i class="material-icons">arrow_back</i>
                Voltar
            </button>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-danger" onclick="limparProdutos()">
                    <i class="material-icons">clear</i>
                    Limpar
                </button>
                <button type="button" class="btn btn-success" onclick="gravarProdutos()">
                    <i class="material-icons">save</i>
                    Gravar
                </button>
            </div>
        </div>
    </div>
</div>
```

## 🧪 Teste Após Migração

1. **Abrir a página** no navegador
2. **Verificar se todas as seções abrem/fecham**
3. **Testar todos os campos**
4. **Verificar se os botões funcionam**
5. **Testar responsividade** (redimensionar janela)
6. **Verificar console** para erros JavaScript

## 🔧 Ajustes Finais

### **Remover Script Temporário**
Após confirmar que tudo funciona, remover o script de conversão automática:

```html
<!-- REMOVER ESTE BLOCO -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (typeof convertMaterializeToModern === 'function') {
            convertMaterializeToModern();
        }
    }, 500);
});
</script>
```

### **Ajustar Estilos Específicos**
Se necessário, adicionar CSS customizado:

```html
<style>
/* Ajustes específicos do projeto */
.custom-spacing {
    margin-bottom: 20px;
}
</style>
```

## 🚨 Solução de Problemas

### **Problema: Accordion não abre**
- Verificar se o `produtos-modern.js` foi carregado
- Verificar console para erros JavaScript

### **Problema: Estilos não aplicados**
- Verificar se o `produtos-modern.css` foi carregado
- Verificar se não há conflitos com CSS antigo

### **Problema: Campos não funcionam**
- Verificar se os IDs dos campos estão corretos
- Verificar se as funções JavaScript existem

## ✅ Checklist Final

- [ ] Backup realizado
- [ ] CSS moderno incluído
- [ ] JavaScript moderno incluído
- [ ] Estrutura HTML convertida
- [ ] Todas as seções funcionando
- [ ] Botões funcionando
- [ ] Campos validando
- [ ] Responsividade OK
- [ ] Console sem erros
- [ ] Funcionalidades preservadas

---

**🎉 Parabéns! Sua tela de produtos agora está modernizada!**
